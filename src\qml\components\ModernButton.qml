import QtQuick 2.15
import QtQuick.Controls 2.15
import QtGraphicalEffects 1.15
import "../theme"

Button {
    id: control
    
    // Custom properties
    property color backgroundColor: Colors.surface
    property color backgroundColorHover: Colors.hover
    property color backgroundColorPressed: Colors.pressed
    property color textColor: Colors.onSurface
    property color borderColor: Colors.border
    property bool elevated: false
    property string iconSource: ""
    property int iconSize: AppTheme.icon.md
    
    implicitWidth: Math.max(AppTheme.button.minWidth, contentItem.implicitWidth + leftPadding + rightPadding)
    implicitHeight: AppTheme.button.heightMedium
    
    leftPadding: AppTheme.button.paddingHorizontal
    rightPadding: AppTheme.button.paddingHorizontal
    topPadding: AppTheme.button.paddingVertical
    bottomPadding: AppTheme.button.paddingVertical
    
    background: Rectangle {
        id: backgroundRect
        radius: AppTheme.button.borderRadius
        color: control.down ? backgroundColorPressed : 
               control.hovered ? backgroundColorHover : backgroundColor
        border.color: borderColor
        border.width: AppTheme.border.widthThin
        
        // Elevation shadow for elevated buttons
        layer.enabled: elevated
        layer.effect: DropShadow {
            horizontalOffset: AppTheme.shadow.elevation2.offsetX
            verticalOffset: AppTheme.shadow.elevation2.offsetY
            radius: AppTheme.shadow.elevation2.blurRadius
            color: AppTheme.shadow.elevation2.color
        }
        
        // Smooth color transitions
        Behavior on color {
            ColorAnimation {
                duration: AppTheme.animations.hover.duration
                easing.type: AppTheme.animations.hover.easing
            }
        }
        
        // Ripple effect on press
        Rectangle {
            id: ripple
            anchors.centerIn: parent
            width: 0
            height: 0
            radius: width / 2
            color: Colors.onSurface
            opacity: 0
            
            states: State {
                name: "pressed"
                when: control.pressed
                PropertyChanges {
                    target: ripple
                    width: Math.max(parent.width, parent.height) * 2
                    height: width
                    opacity: 0.1
                }
            }
            
            transitions: Transition {
                to: "pressed"
                ParallelAnimation {
                    NumberAnimation {
                        properties: "width,height"
                        duration: AppTheme.animations.press.duration
                        easing.type: AppTheme.animations.press.easing
                    }
                    NumberAnimation {
                        property: "opacity"
                        duration: AppTheme.animations.press.duration
                        easing.type: AppTheme.animations.press.easing
                    }
                }
            }
        }
    }
    
    contentItem: Row {
        spacing: AppTheme.spacing.sm
        
        Image {
            id: icon
            visible: iconSource !== ""
            source: iconSource
            width: iconSize
            height: iconSize
            anchors.verticalCenter: parent.verticalCenter
            fillMode: Image.PreserveAspectFit
            
            ColorOverlay {
                anchors.fill: parent
                source: parent
                color: textColor
            }
        }
        
        Text {
            text: control.text
            font.family: AppTheme.typography.button.fontFamily
            font.pixelSize: AppTheme.typography.button.fontSize
            font.weight: AppTheme.typography.button.fontWeight
            font.letterSpacing: AppTheme.typography.button.letterSpacing
            color: textColor
            anchors.verticalCenter: parent.verticalCenter
            
            Behavior on color {
                ColorAnimation {
                    duration: AppTheme.animations.hover.duration
                    easing.type: AppTheme.animations.hover.easing
                }
            }
        }
    }
    
    // Focus indicator
    Rectangle {
        anchors.fill: parent
        radius: AppTheme.button.borderRadius
        color: "transparent"
        border.color: Colors.focus
        border.width: 2
        visible: control.activeFocus
        opacity: 0.6
    }
    
    // Scale animation on press
    scale: pressed ? 0.98 : 1.0
    Behavior on scale {
        NumberAnimation {
            duration: AppTheme.animations.press.duration
            easing.type: AppTheme.animations.press.easing
        }
    }
}
