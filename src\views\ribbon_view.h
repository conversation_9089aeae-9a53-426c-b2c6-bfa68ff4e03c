#pragma once

#include <QTabWidget>
#include <QToolButton>
#include <QWidget>
#include <memory>

namespace CANoeLite::ViewModels {
class RibbonViewModel;
}

namespace CANoeLite::Views {

/**
 * @brief Ribbon view widget
 */
class RibbonView : public QWidget {
    Q_OBJECT

public:
    explicit RibbonView(std::shared_ptr<ViewModels::RibbonViewModel> view_model, QWidget* parent = nullptr);
    ~RibbonView() override = default;

private slots:
    void on_quick_access_clicked();
    void on_tab_changed(int index);
    void on_button_clicked();

private:
    void setup_ui();
    void setup_connections();
    void create_ribbon_tabs();
    void create_tab_content(const QString& tab_name, const QVariantMap& tab_data);
    QWidget* create_panel(const QVariantMap& panel_data);

    std::shared_ptr<ViewModels::RibbonViewModel> view_model_;
    QToolButton* quick_access_button_;
    QTabWidget* tab_widget_;
};

}  // namespace CANoeLite::Views
