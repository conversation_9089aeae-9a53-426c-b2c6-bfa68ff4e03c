import QtQuick 2.15
import "../theme"

Rectangle {
    id: root
    
    property string text: ""
    property color textColor: Colors.statusText
    property color backgroundColor: "transparent"
    property color borderColor: Colors.separator
    
    color: backgroundColor
    border.color: borderColor
    border.width: text !== "" ? AppTheme.border.widthThin : 0
    radius: AppTheme.radius.xs
    
    Text {
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.verticalCenter: parent.verticalCenter
        anchors.margins: AppTheme.spacing.sm
        
        text: root.text
        font.family: AppTheme.typography.status.fontFamily
        font.pixelSize: AppTheme.typography.status.textFontSize
        font.weight: AppTheme.typography.status.textFontWeight
        color: textColor
        elide: Text.ElideRight
        verticalAlignment: Text.AlignVCenter
    }
}
