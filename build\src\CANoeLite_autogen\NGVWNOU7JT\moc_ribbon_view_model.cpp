/****************************************************************************
** Meta object code from reading C++ file 'ribbon_view_model.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.1)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../src/view_models/ribbon_view_model.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ribbon_view_model.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.1. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN9CANoeLite10ViewModels15RibbonViewModelE_t {};
} // unnamed namespace

template <> constexpr inline auto CANoeLite::ViewModels::RibbonViewModel::qt_create_metaobjectdata<qt_meta_tag_ZN9CANoeLite10ViewModels15RibbonViewModelE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "CANoeLite::ViewModels::RibbonViewModel",
        "quick_access_visibility_changed",
        "",
        "visible",
        "current_tab_changed",
        "tab",
        "action_executed",
        "action",
        "ribbonDataChanged",
        "quick_access_visible_changed",
        "set_quick_access_visible",
        "set_current_tab",
        "execute_action",
        "executeCommand",
        "ribbonTabs",
        "QVariantList",
        "quickAccessVisible",
        "currentTab"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'quick_access_visibility_changed'
        QtMocHelpers::SignalData<void(bool)>(1, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 3 },
        }}),
        // Signal 'current_tab_changed'
        QtMocHelpers::SignalData<void(const QString &)>(4, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 5 },
        }}),
        // Signal 'action_executed'
        QtMocHelpers::SignalData<void(const QString &)>(6, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 7 },
        }}),
        // Signal 'ribbonDataChanged'
        QtMocHelpers::SignalData<void()>(8, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'quick_access_visible_changed'
        QtMocHelpers::SignalData<void(bool)>(9, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 3 },
        }}),
        // Slot 'set_quick_access_visible'
        QtMocHelpers::SlotData<void(bool)>(10, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 3 },
        }}),
        // Slot 'set_current_tab'
        QtMocHelpers::SlotData<void(const QString &)>(11, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 5 },
        }}),
        // Slot 'execute_action'
        QtMocHelpers::SlotData<void(const QString &)>(12, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 7 },
        }}),
        // Slot 'executeCommand'
        QtMocHelpers::SlotData<void(const QString &)>(13, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 7 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'ribbonTabs'
        QtMocHelpers::PropertyData<QVariantList>(14, 0x80000000 | 15, QMC::DefaultPropertyFlags | QMC::EnumOrFlag, 3),
        // property 'quickAccessVisible'
        QtMocHelpers::PropertyData<bool>(16, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable, 4),
        // property 'currentTab'
        QtMocHelpers::PropertyData<QString>(17, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable, 1),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<RibbonViewModel, qt_meta_tag_ZN9CANoeLite10ViewModels15RibbonViewModelE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject CANoeLite::ViewModels::RibbonViewModel::staticMetaObject = { {
    QMetaObject::SuperData::link<Core::IViewModel::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN9CANoeLite10ViewModels15RibbonViewModelE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN9CANoeLite10ViewModels15RibbonViewModelE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN9CANoeLite10ViewModels15RibbonViewModelE_t>.metaTypes,
    nullptr
} };

void CANoeLite::ViewModels::RibbonViewModel::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<RibbonViewModel *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->quick_access_visibility_changed((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 1: _t->current_tab_changed((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 2: _t->action_executed((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 3: _t->ribbonDataChanged(); break;
        case 4: _t->quick_access_visible_changed((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 5: _t->set_quick_access_visible((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 6: _t->set_current_tab((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 7: _t->execute_action((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 8: _t->executeCommand((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (RibbonViewModel::*)(bool )>(_a, &RibbonViewModel::quick_access_visibility_changed, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (RibbonViewModel::*)(const QString & )>(_a, &RibbonViewModel::current_tab_changed, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (RibbonViewModel::*)(const QString & )>(_a, &RibbonViewModel::action_executed, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (RibbonViewModel::*)()>(_a, &RibbonViewModel::ribbonDataChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (RibbonViewModel::*)(bool )>(_a, &RibbonViewModel::quick_access_visible_changed, 4))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<QVariantList*>(_v) = _t->ribbon_tabs(); break;
        case 1: *reinterpret_cast<bool*>(_v) = _t->quick_access_visible(); break;
        case 2: *reinterpret_cast<QString*>(_v) = _t->current_tab(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 1: _t->set_quick_access_visible(*reinterpret_cast<bool*>(_v)); break;
        case 2: _t->set_current_tab(*reinterpret_cast<QString*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *CANoeLite::ViewModels::RibbonViewModel::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *CANoeLite::ViewModels::RibbonViewModel::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN9CANoeLite10ViewModels15RibbonViewModelE_t>.strings))
        return static_cast<void*>(this);
    return Core::IViewModel::qt_metacast(_clname);
}

int CANoeLite::ViewModels::RibbonViewModel::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = Core::IViewModel::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 9)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 9;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 9)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 9;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 3;
    }
    return _id;
}

// SIGNAL 0
void CANoeLite::ViewModels::RibbonViewModel::quick_access_visibility_changed(bool _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 0, nullptr, _t1);
}

// SIGNAL 1
void CANoeLite::ViewModels::RibbonViewModel::current_tab_changed(const QString & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 1, nullptr, _t1);
}

// SIGNAL 2
void CANoeLite::ViewModels::RibbonViewModel::action_executed(const QString & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 2, nullptr, _t1);
}

// SIGNAL 3
void CANoeLite::ViewModels::RibbonViewModel::ribbonDataChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void CANoeLite::ViewModels::RibbonViewModel::quick_access_visible_changed(bool _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 4, nullptr, _t1);
}
QT_WARNING_POP
