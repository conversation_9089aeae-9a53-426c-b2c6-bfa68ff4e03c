#include "services/settings_service.h"

namespace CANoeLite::Services {

SettingsService::SettingsService() {
    settings_ = std::make_unique<QSettings>();
}

QVariant SettingsService::get_value(const QString& key, const QVariant& default_value) const {
    return settings_->value(key, default_value);
}

void SettingsService::set_value(const QString& key, const QVariant& value) {
    settings_->setValue(key, value);
}

void SettingsService::sync() {
    settings_->sync();
}

}  // namespace CANoeLite::Services
