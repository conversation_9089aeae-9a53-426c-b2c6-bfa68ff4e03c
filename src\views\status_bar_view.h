#pragma once

#include <QLabel>
#include <QProgressBar>
#include <QStatusBar>
#include <memory>

namespace CANoeLite::ViewModels {
class MainWindowViewModel;
}

namespace CANoeLite::Views {

/**
 * @brief Status bar view
 */
class StatusBarView : public QStatusBar {
    Q_OBJECT

public:
    explicit StatusBarView(std::shared_ptr<ViewModels::MainWindowViewModel> view_model, QWidget* parent = nullptr);
    ~StatusBarView() override = default;

private slots:
    void on_status_text_changed(const QString& text);
    void on_can_bitrate_changed(const QString& bitrate);
    void on_clock_time_changed(const QString& time);

private:
    void setup_ui();
    void setup_connections();

    std::shared_ptr<ViewModels::MainWindowViewModel> view_model_;
    QLabel* status_label_;
    QProgressBar* progress_bar_;
    QLabel* can_bitrate_label_;
    QLabel* clock_label_;
};

}  // namespace CANoeLite::Views
