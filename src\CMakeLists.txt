# Source files
set(SOURCES
    main.cpp
    
    # Core
    core/application.cpp
    core/application.h
    core/interfaces.h
    
    # ViewModels
    view_models/main_window_view_model.cpp
    view_models/main_window_view_model.h
    view_models/ribbon_view_model.cpp
    view_models/ribbon_view_model.h
    view_models/dock_widget_view_model.cpp
    view_models/dock_widget_view_model.h
    
    # Views (removed - using QML instead)
    
    # Services
    services/dock_manager.cpp
    services/dock_manager.h
    services/ribbon_config_service.cpp
    services/ribbon_config_service.h
    services/settings_service.cpp
    services/settings_service.h
)

# Resources
set(RESOURCES
    resources/resources.qrc
    qml/qml.qrc
)

# Create executable
add_executable(CANoeLite ${SOURCES} ${RESOURCES})

# Link Qt libraries
target_link_libraries(CANoeLite Qt6::Core Qt6::Widgets Qt6::Quick Qt6::Qml Qt6::QuickControls2)
