# Source files
set(SOURCES
    main.cpp
    
    # Core
    core/application.cpp
    core/application.h
    core/interfaces.h
    
    # ViewModels
    view_models/main_window_view_model.cpp
    view_models/main_window_view_model.h
    view_models/ribbon_view_model.cpp
    view_models/ribbon_view_model.h
    view_models/dock_widget_view_model.cpp
    view_models/dock_widget_view_model.h
    
    # Views
    views/main_window.cpp
    views/main_window.h
    views/ribbon_view.cpp
    views/ribbon_view.h
    views/desktop_tabs_widget.cpp
    views/desktop_tabs_widget.h
    views/trace_dock_widget.cpp
    views/trace_dock_widget.h
    views/status_bar_view.cpp
    views/status_bar_view.h
    
    # Services
    services/dock_manager.cpp
    services/dock_manager.h
    services/ribbon_config_service.cpp
    services/ribbon_config_service.h
    services/settings_service.cpp
    services/settings_service.h
)

# Resources
set(RESOURCES
    resources/resources.qrc
)

# Create executable
add_executable(CANoeLite ${SOURCES} ${RESOURCES})

# Link Qt libraries
target_link_libraries(CANoeLite Qt6::Core Qt6::Widgets)
