import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        name: "Array"
        accessSemantics: "reference"
        prototype: "ArrayPrototype"
        isJavaScriptBuiltin: true
        Property { name: "length"; type: "number" }
    }
    Component {
        name: "ArrayBuffer"
        accessSemantics: "reference"
        prototype: "ArrayBufferPrototype"
        isJavaScriptBuiltin: true
    }
    Component {
        name: "ArrayBufferPrototype"
        accessSemantics: "reference"
        prototype: "ObjectPrototype"
        isJavaScriptBuiltin: true
        Property { name: "byteLength"; type: "number" }
        Method {
            name: "constructor"
            type: "ArrayBuffer"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "slice"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method { name: "toString"; isJavaScriptFunction: true }
    }
    Component {
        name: "ArrayPrototype"
        accessSemantics: "reference"
        prototype: "ObjectPrototype"
        isJavaScriptBuiltin: true
        Property { name: "length"; type: "number" }
        Method {
            name: "constructor"
            type: "Array"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
        }
        Method { name: "toString"; isJavaScriptFunction: true }
        Method { name: "toLocaleString"; isJavaScriptFunction: true }
        Method {
            name: "concat"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "copyWithin"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method { name: "entries"; isJavaScriptFunction: true }
        Method {
            name: "fill"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "find"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "findIndex"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "includes"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "join"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method { name: "keys"; isJavaScriptFunction: true }
        Method { name: "pop"; isJavaScriptFunction: true }
        Method {
            name: "push"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method { name: "reverse"; isJavaScriptFunction: true }
        Method { name: "shift"; isJavaScriptFunction: true }
        Method {
            name: "slice"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "sort"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "splice"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "unshift"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "indexOf"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "lastIndexOf"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "every"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "some"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "forEach"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "map"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "filter"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "reduce"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "reduceRight"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method { name: "values"; isJavaScriptFunction: true }
    }
    Component {
        name: "Atomics"
        accessSemantics: "reference"
        prototype: "ObjectPrototype"
        isJavaScriptBuiltin: true
        Method {
            name: "add"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
        }
        Method {
            name: "and"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
        }
        Method {
            name: "compareExchange"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
            Parameter {}
        }
        Method {
            name: "exchange"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
        }
        Method {
            name: "isLockFree"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "load"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "or"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
        }
        Method {
            name: "store"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
        }
        Method {
            name: "sub"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
        }
        Method {
            name: "wait"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
            Parameter {}
        }
        Method {
            name: "wake"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
        }
        Method {
            name: "xor"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
        }
    }
    Component {
        name: "Boolean"
        accessSemantics: "reference"
        prototype: "BooleanPrototype"
        isJavaScriptBuiltin: true
    }
    Component {
        name: "BooleanPrototype"
        accessSemantics: "reference"
        prototype: "ObjectPrototype"
        isJavaScriptBuiltin: true
        Method {
            name: "constructor"
            type: "Boolean"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
        }
        Method { name: "toString"; isJavaScriptFunction: true }
        Method { name: "valueOf"; isJavaScriptFunction: true }
    }
    Component {
        name: "Console"
        accessSemantics: "reference"
        prototype: "ObjectPrototype"
        isJavaScriptBuiltin: true
        Method { name: "debug"; isJavaScriptFunction: true }
        Method { name: "log"; isJavaScriptFunction: true }
        Method { name: "info"; isJavaScriptFunction: true }
        Method { name: "warn"; isJavaScriptFunction: true }
        Method { name: "error"; isJavaScriptFunction: true }
        Method { name: "assert"; isJavaScriptFunction: true }
        Method { name: "count"; isJavaScriptFunction: true }
        Method { name: "profile"; isJavaScriptFunction: true }
        Method { name: "profileEnd"; isJavaScriptFunction: true }
        Method { name: "time"; isJavaScriptFunction: true }
        Method { name: "timeEnd"; isJavaScriptFunction: true }
        Method { name: "trace"; isJavaScriptFunction: true }
        Method { name: "exception"; isJavaScriptFunction: true }
    }
    Component {
        name: "DataView"
        accessSemantics: "reference"
        prototype: "DataViewPrototype"
        isJavaScriptBuiltin: true
    }
    Component {
        name: "DataViewPrototype"
        accessSemantics: "reference"
        prototype: "ObjectPrototype"
        isJavaScriptBuiltin: true
        Property { name: "buffer"; type: "DataViewPrototypeBuffer" }
        Property { name: "byteLength"; type: "number" }
        Property { name: "byteOffset"; type: "number" }
        Method {
            name: "constructor"
            type: "DataView"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "getInt8"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "getUint8"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "getInt16"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "getUint16"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "getInt32"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "getUint32"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "getFloat32"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "getFloat64"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "setInt8"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "setUint8"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "setInt16"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "setUint16"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "setInt32"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "setUint32"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "setFloat32"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "setFloat64"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "getUInt8"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "getUInt16"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "getUInt32"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "setUInt8"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "setUInt16"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "setUInt32"
            isJavaScriptFunction: true
            Parameter {}
        }
    }
    Component {
        name: "DataViewPrototypeBuffer"
        accessSemantics: "reference"
        prototype: "ArrayBufferPrototype"
        isJavaScriptBuiltin: true
    }
    Component {
        name: "Date"
        accessSemantics: "reference"
        prototype: "DatePrototype"
        isJavaScriptBuiltin: true
    }
    Component {
        name: "DatePrototype"
        accessSemantics: "reference"
        prototype: "ObjectPrototype"
        isJavaScriptBuiltin: true
        Method {
            name: "constructor"
            type: "Date"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
            Parameter {}
            Parameter {}
            Parameter {}
            Parameter {}
        }
        Method { name: "toString"; isJavaScriptFunction: true }
        Method { name: "toDateString"; isJavaScriptFunction: true }
        Method { name: "toTimeString"; isJavaScriptFunction: true }
        Method { name: "toLocaleString"; isJavaScriptFunction: true }
        Method { name: "toLocaleDateString"; isJavaScriptFunction: true }
        Method { name: "toLocaleTimeString"; isJavaScriptFunction: true }
        Method { name: "valueOf"; isJavaScriptFunction: true }
        Method { name: "getTime"; isJavaScriptFunction: true }
        Method { name: "getYear"; isJavaScriptFunction: true }
        Method { name: "getFullYear"; isJavaScriptFunction: true }
        Method { name: "getUTCFullYear"; isJavaScriptFunction: true }
        Method { name: "getMonth"; isJavaScriptFunction: true }
        Method { name: "getUTCMonth"; isJavaScriptFunction: true }
        Method { name: "getDate"; isJavaScriptFunction: true }
        Method { name: "getUTCDate"; isJavaScriptFunction: true }
        Method { name: "getDay"; isJavaScriptFunction: true }
        Method { name: "getUTCDay"; isJavaScriptFunction: true }
        Method { name: "getHours"; isJavaScriptFunction: true }
        Method { name: "getUTCHours"; isJavaScriptFunction: true }
        Method { name: "getMinutes"; isJavaScriptFunction: true }
        Method { name: "getUTCMinutes"; isJavaScriptFunction: true }
        Method { name: "getSeconds"; isJavaScriptFunction: true }
        Method { name: "getUTCSeconds"; isJavaScriptFunction: true }
        Method { name: "getMilliseconds"; isJavaScriptFunction: true }
        Method { name: "getUTCMilliseconds"; isJavaScriptFunction: true }
        Method { name: "getTimezoneOffset"; isJavaScriptFunction: true }
        Method {
            name: "setTime"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "setMilliseconds"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "setUTCMilliseconds"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "setSeconds"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "setUTCSeconds"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "setMinutes"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
        }
        Method {
            name: "setUTCMinutes"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
        }
        Method {
            name: "setHours"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
            Parameter {}
        }
        Method {
            name: "setUTCHours"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
            Parameter {}
        }
        Method {
            name: "setDate"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "setUTCDate"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "setMonth"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "setUTCMonth"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "setYear"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "setFullYear"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
        }
        Method {
            name: "setUTCFullYear"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
        }
        Method { name: "toUTCString"; isJavaScriptFunction: true }
        Method { name: "toGMTString"; isJavaScriptFunction: true }
        Method { name: "toISOString"; isJavaScriptFunction: true }
        Method {
            name: "toJSON"
            isJavaScriptFunction: true
            Parameter {}
        }
    }
    Component {
        name: "Error"
        accessSemantics: "reference"
        prototype: "ErrorPrototype"
        isJavaScriptBuiltin: true
        Property { name: "stack"; type: "string" }
        Property { name: "fileName" }
        Property { name: "lineNumber" }
    }
    Component {
        name: "ErrorPrototype"
        accessSemantics: "reference"
        prototype: "ObjectPrototype"
        isJavaScriptBuiltin: true
        Property { name: "message"; type: "string" }
        Property { name: "name"; type: "string" }
        Method {
            name: "constructor"
            type: "Error"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
        }
        Method { name: "toString"; isJavaScriptFunction: true }
    }
    Component {
        name: "EvalError"
        accessSemantics: "reference"
        prototype: "EvalErrorPrototype"
        isJavaScriptBuiltin: true
        Property { name: "stack"; type: "string" }
        Property { name: "fileName" }
        Property { name: "lineNumber" }
    }
    Component {
        name: "EvalErrorPrototype"
        accessSemantics: "reference"
        prototype: "ErrorPrototype"
        isJavaScriptBuiltin: true
        Property { name: "message"; type: "string" }
        Property { name: "name"; type: "string" }
        Method {
            name: "constructor"
            type: "EvalError"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
        }
        Method { name: "toString"; isJavaScriptFunction: true }
    }
    Component {
        name: "Float32Array"
        accessSemantics: "reference"
        prototype: "Float32ArrayPrototype"
        isJavaScriptBuiltin: true
    }
    Component {
        name: "Float32ArrayPrototype"
        accessSemantics: "reference"
        prototype: "IntrinsicTypedArrayPrototype"
        isJavaScriptBuiltin: true
        Property { name: "BYTES_PER_ELEMENT"; type: "number"; isReadonly: true }
        Method {
            name: "constructor"
            type: "Float32Array"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
        }
    }
    Component {
        name: "Float64Array"
        accessSemantics: "reference"
        prototype: "Float64ArrayPrototype"
        isJavaScriptBuiltin: true
    }
    Component {
        name: "Float64ArrayPrototype"
        accessSemantics: "reference"
        prototype: "IntrinsicTypedArrayPrototype"
        isJavaScriptBuiltin: true
        Property { name: "BYTES_PER_ELEMENT"; type: "number"; isReadonly: true }
        Method {
            name: "constructor"
            type: "Float64Array"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
        }
    }
    Component {
        name: "Function"
        accessSemantics: "reference"
        prototype: "FunctionPrototype"
        isJavaScriptBuiltin: true
        Property { name: "prototype"; type: "Object" }
        Property { name: "name"; type: "string"; isReadonly: true }
        Property { name: "length"; type: "number"; isReadonly: true }
    }
    Component {
        name: "FunctionPrototype"
        accessSemantics: "reference"
        prototype: "ObjectPrototype"
        isJavaScriptBuiltin: true
        Property { name: "name"; type: "string"; isReadonly: true }
        Property { name: "length"; type: "number"; isReadonly: true }
        Property { name: "caller" }
        Property { name: "arguments" }
        Method {
            name: "constructor"
            type: "Function"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
        }
        Method { name: "toString"; isJavaScriptFunction: true }
        Method {
            name: "apply"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "call"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "bind"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method { name: "connect"; isJavaScriptFunction: true }
        Method { name: "disconnect"; isJavaScriptFunction: true }
    }
    Component {
        name: "GlobalObject"
        accessSemantics: "reference"
        prototype: "ObjectPrototype"
        isJavaScriptBuiltin: true
        Property { name: "Atomics"; type: "Atomics" }
        Property { name: "Math"; type: "Math" }
        Property { name: "JSON"; type: "JSON" }
        Property { name: "Reflect"; type: "Reflect" }
        Property { name: "undefined"; isReadonly: true }
        Property { name: "NaN"; type: "number"; isReadonly: true }
        Property { name: "Infinity"; type: "number"; isReadonly: true }
        Property { name: "Qt"; type: "Qt" }
        Property { name: "console"; type: "Console" }
        Method {
            name: "Object"
            type: "Object"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "String"
            type: "String"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
        }
        Method { name: "Symbol"; type: "undefined"; isConstructor: true; isJavaScriptFunction: true }
        Method {
            name: "Number"
            type: "Number"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "Boolean"
            type: "Boolean"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "Array"
            type: "Array"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "Function"
            type: "Function"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "Date"
            type: "Date"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
            Parameter {}
            Parameter {}
            Parameter {}
            Parameter {}
        }
        Method {
            name: "RegExp"
            type: "RegExp"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "Error"
            type: "Error"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "EvalError"
            type: "EvalError"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "RangeError"
            type: "RangeError"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "ReferenceError"
            type: "ReferenceError"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "SyntaxError"
            type: "SyntaxError"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "TypeError"
            type: "TypeError"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "URIError"
            type: "URIError"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "Promise"
            type: "Promise"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
        }
        Method { name: "URL"; type: "URL"; isConstructor: true; isJavaScriptFunction: true }
        Method {
            name: "URLSearchParams"
            type: "URLSearchParams"
            isConstructor: true
            isJavaScriptFunction: true
        }
        Method {
            name: "SharedArrayBuffer"
            type: "SharedArrayBuffer"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "ArrayBuffer"
            type: "ArrayBuffer"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "DataView"
            type: "DataView"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
        }
        Method { name: "WeakSet"; type: "WeakSet"; isConstructor: true; isJavaScriptFunction: true }
        Method { name: "Set"; type: "Set"; isConstructor: true; isJavaScriptFunction: true }
        Method { name: "WeakMap"; type: "WeakMap"; isConstructor: true; isJavaScriptFunction: true }
        Method { name: "Map"; type: "Map"; isConstructor: true; isJavaScriptFunction: true }
        Method {
            name: "Int8Array"
            type: "Int8Array"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
        }
        Method {
            name: "Uint8Array"
            type: "Uint8Array"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
        }
        Method {
            name: "Int16Array"
            type: "Int16Array"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
        }
        Method {
            name: "Uint16Array"
            type: "Uint16Array"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
        }
        Method {
            name: "Int32Array"
            type: "Int32Array"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
        }
        Method {
            name: "Uint32Array"
            type: "Uint32Array"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
        }
        Method {
            name: "Uint8ClampedArray"
            type: "Uint8ClampedArray"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
        }
        Method {
            name: "Float32Array"
            type: "Float32Array"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
        }
        Method {
            name: "Float64Array"
            type: "Float64Array"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
        }
        Method {
            name: "Proxy"
            type: "Proxy"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "eval"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "parseInt"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "parseFloat"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "isNaN"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "isFinite"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "decodeURI"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "decodeURIComponent"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "encodeURI"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "encodeURIComponent"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "escape"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "unescape"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method { name: "qsTranslate"; isJavaScriptFunction: true }
        Method { name: "QT_TRANSLATE_NOOP"; isJavaScriptFunction: true }
        Method { name: "qsTr"; isJavaScriptFunction: true }
        Method { name: "QT_TR_NOOP"; isJavaScriptFunction: true }
        Method { name: "qsTrId"; isJavaScriptFunction: true }
        Method { name: "QT_TRID_NOOP"; isJavaScriptFunction: true }
        Method { name: "print"; isJavaScriptFunction: true }
        Method { name: "gc"; isJavaScriptFunction: true }
    }
    Component {
        name: "Int16Array"
        accessSemantics: "reference"
        prototype: "Int16ArrayPrototype"
        isJavaScriptBuiltin: true
    }
    Component {
        name: "Int16ArrayPrototype"
        accessSemantics: "reference"
        prototype: "IntrinsicTypedArrayPrototype"
        isJavaScriptBuiltin: true
        Property { name: "BYTES_PER_ELEMENT"; type: "number"; isReadonly: true }
        Method {
            name: "constructor"
            type: "Int16Array"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
        }
    }
    Component {
        name: "Int32Array"
        accessSemantics: "reference"
        prototype: "Int32ArrayPrototype"
        isJavaScriptBuiltin: true
    }
    Component {
        name: "Int32ArrayPrototype"
        accessSemantics: "reference"
        prototype: "IntrinsicTypedArrayPrototype"
        isJavaScriptBuiltin: true
        Property { name: "BYTES_PER_ELEMENT"; type: "number"; isReadonly: true }
        Method {
            name: "constructor"
            type: "Int32Array"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
        }
    }
    Component {
        name: "Int8Array"
        accessSemantics: "reference"
        prototype: "Int8ArrayPrototype"
        isJavaScriptBuiltin: true
    }
    Component {
        name: "Int8ArrayPrototype"
        accessSemantics: "reference"
        prototype: "IntrinsicTypedArrayPrototype"
        isJavaScriptBuiltin: true
        Property { name: "BYTES_PER_ELEMENT"; type: "number"; isReadonly: true }
        Method {
            name: "constructor"
            type: "Int8Array"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
        }
    }
    Component {
        name: "IntrinsicTypedArrayPrototype"
        accessSemantics: "reference"
        prototype: "ObjectPrototype"
        isJavaScriptBuiltin: true
        Property { name: "buffer" }
        Property { name: "byteLength" }
        Property { name: "byteOffset" }
        Property { name: "length" }
        Method {
            name: "copyWithin"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method { name: "entries"; isJavaScriptFunction: true }
        Method {
            name: "every"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "fill"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "filter"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "find"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "findIndex"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "forEach"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "includes"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "indexOf"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "join"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method { name: "keys"; isJavaScriptFunction: true }
        Method {
            name: "lastIndexOf"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "map"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "reduce"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "reduceRight"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method { name: "reverse"; isJavaScriptFunction: true }
        Method {
            name: "some"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "set"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "slice"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "subarray"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method { name: "toLocaleString"; isJavaScriptFunction: true }
        Method { name: "toString"; isJavaScriptFunction: true }
        Method { name: "values"; isJavaScriptFunction: true }
    }
    Component {
        name: "JSON"
        accessSemantics: "reference"
        prototype: "ObjectPrototype"
        isJavaScriptBuiltin: true
        Method {
            name: "parse"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "stringify"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
        }
    }
    Component {
        name: "Map"
        accessSemantics: "reference"
        prototype: "MapPrototype"
        isJavaScriptBuiltin: true
    }
    Component {
        name: "MapPrototype"
        accessSemantics: "reference"
        prototype: "ObjectPrototype"
        isJavaScriptBuiltin: true
        Property { name: "size"; type: "number" }
        Method { name: "constructor"; type: "Map"; isConstructor: true; isJavaScriptFunction: true }
        Method { name: "clear"; isJavaScriptFunction: true }
        Method {
            name: "delete"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "forEach"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "get"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "has"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method { name: "keys"; isJavaScriptFunction: true }
        Method {
            name: "set"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method { name: "values"; isJavaScriptFunction: true }
        Method { name: "entries"; isJavaScriptFunction: true }
    }
    Component {
        name: "Math"
        accessSemantics: "reference"
        prototype: "ObjectPrototype"
        isJavaScriptBuiltin: true
        Property { name: "E"; type: "number"; isReadonly: true }
        Property { name: "LN2"; type: "number"; isReadonly: true }
        Property { name: "LN10"; type: "number"; isReadonly: true }
        Property { name: "LOG2E"; type: "number"; isReadonly: true }
        Property { name: "LOG10E"; type: "number"; isReadonly: true }
        Property { name: "PI"; type: "number"; isReadonly: true }
        Property { name: "SQRT1_2"; type: "number"; isReadonly: true }
        Property { name: "SQRT2"; type: "number"; isReadonly: true }
        Method {
            name: "abs"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "acos"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "acosh"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "asin"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "asinh"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "atan"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "atanh"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "atan2"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "cbrt"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "ceil"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "clz32"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "cos"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "cosh"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "exp"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "expm1"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "floor"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "fround"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "hypot"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "imul"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "log"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "log10"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "log1p"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "log2"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "max"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "min"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "pow"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method { name: "random"; isJavaScriptFunction: true }
        Method {
            name: "round"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "sign"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "sin"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "sinh"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "sqrt"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "tan"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "tanh"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "trunc"
            isJavaScriptFunction: true
            Parameter {}
        }
    }
    Component {
        name: "Number"
        accessSemantics: "reference"
        prototype: "NumberPrototype"
        isJavaScriptBuiltin: true
    }
    Component {
        name: "NumberPrototype"
        accessSemantics: "reference"
        prototype: "ObjectPrototype"
        isJavaScriptBuiltin: true
        Method {
            name: "constructor"
            type: "Number"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "toString"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method { name: "toLocaleString"; isJavaScriptFunction: true }
        Method { name: "valueOf"; isJavaScriptFunction: true }
        Method {
            name: "toFixed"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "toExponential"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "toPrecision"
            isJavaScriptFunction: true
            Parameter {}
        }
    }
    Component {
        name: "Object"
        accessSemantics: "reference"
        prototype: "ObjectPrototype"
        isJavaScriptBuiltin: true
    }
    Component {
        name: "ObjectPrototype"
        accessSemantics: "reference"
        isJavaScriptBuiltin: true
        Property { name: "__proto__" }
        Method {
            name: "constructor"
            type: "Object"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
        }
        Method { name: "toString"; isJavaScriptFunction: true }
        Method { name: "toLocaleString"; isJavaScriptFunction: true }
        Method { name: "valueOf"; isJavaScriptFunction: true }
        Method {
            name: "hasOwnProperty"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "isPrototypeOf"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "propertyIsEnumerable"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "__defineGetter__"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "__defineSetter__"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
    }
    Component {
        name: "Promise"
        accessSemantics: "reference"
        prototype: "PromisePrototype"
        isJavaScriptBuiltin: true
    }
    Component {
        name: "PromisePrototype"
        accessSemantics: "reference"
        prototype: "ObjectPrototype"
        isJavaScriptBuiltin: true
        Method {
            name: "constructor"
            type: "Promise"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "then"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "catch"
            isJavaScriptFunction: true
            Parameter {}
        }
    }
    Component {
        name: "Proxy"
        accessSemantics: "reference"
        prototype: "ObjectPrototype"
        isJavaScriptBuiltin: true
    }
    Component {
        name: "Qt"
        accessSemantics: "reference"
        prototype: "QtPrototype"
        isJavaScriptBuiltin: true
        Property { name: "objectName"; type: "string" }
        Property { name: "application"; type: "QtApplication" }
        Property { name: "platform"; type: "QtPlatform" }
        Property { name: "inputMethod"; type: "QtInputMethod" }
        Property { name: "styleHints"; type: "QtStyleHints" }
        Property { name: "uiLanguage"; type: "string" }
        Method { name: "objectNameChanged"; isJavaScriptFunction: true }
        Method { name: "include"; isJavaScriptFunction: true }
        Method { name: "isQtObject"; isJavaScriptFunction: true }
        Method { name: "color"; isJavaScriptFunction: true }
        Method { name: "rgba"; isJavaScriptFunction: true }
        Method { name: "hsla"; isJavaScriptFunction: true }
        Method { name: "hsva"; isJavaScriptFunction: true }
        Method { name: "colorEqual"; isJavaScriptFunction: true }
        Method { name: "rect"; isJavaScriptFunction: true }
        Method { name: "point"; isJavaScriptFunction: true }
        Method { name: "size"; isJavaScriptFunction: true }
        Method { name: "vector2d"; isJavaScriptFunction: true }
        Method { name: "vector3d"; isJavaScriptFunction: true }
        Method { name: "vector4d"; isJavaScriptFunction: true }
        Method { name: "quaternion"; isJavaScriptFunction: true }
        Method { name: "matrix4x4"; isJavaScriptFunction: true }
        Method { name: "lighter"; isJavaScriptFunction: true }
        Method { name: "darker"; isJavaScriptFunction: true }
        Method { name: "alpha"; isJavaScriptFunction: true }
        Method { name: "tint"; isJavaScriptFunction: true }
        Method { name: "formatDate"; isJavaScriptFunction: true }
        Method { name: "formatTime"; isJavaScriptFunction: true }
        Method { name: "formatDateTime"; isJavaScriptFunction: true }
        Method { name: "locale"; isJavaScriptFunction: true }
        Method { name: "url"; isJavaScriptFunction: true }
        Method { name: "resolvedUrl"; isJavaScriptFunction: true }
        Method { name: "openUrlExternally"; isJavaScriptFunction: true }
        Method { name: "font"; isJavaScriptFunction: true }
        Method { name: "fontFamilies"; isJavaScriptFunction: true }
        Method { name: "md5"; isJavaScriptFunction: true }
        Method { name: "btoa"; isJavaScriptFunction: true }
        Method { name: "atob"; isJavaScriptFunction: true }
        Method { name: "quit"; isJavaScriptFunction: true }
        Method { name: "exit"; isJavaScriptFunction: true }
        Method { name: "createQmlObject"; isJavaScriptFunction: true }
        Method { name: "createComponent"; isJavaScriptFunction: true }
        Method { name: "binding"; isJavaScriptFunction: true }
        Method { name: "callLater"; isJavaScriptFunction: true }
    }
    Component {
        name: "QtApplication"
        accessSemantics: "reference"
        prototype: "ObjectPrototype"
        isJavaScriptBuiltin: true
        Property { name: "objectName"; type: "string" }
        Property { name: "arguments"; type: "QtApplicationArguments" }
        Property { name: "name"; type: "string" }
        Property { name: "version"; type: "string" }
        Property { name: "organization"; type: "string" }
        Property { name: "domain"; type: "string" }
        Method { name: "objectNameChanged"; isJavaScriptFunction: true }
        Method { name: "aboutToQuit"; isJavaScriptFunction: true }
        Method { name: "nameChanged"; isJavaScriptFunction: true }
        Method { name: "versionChanged"; isJavaScriptFunction: true }
        Method { name: "organizationChanged"; isJavaScriptFunction: true }
        Method { name: "domainChanged"; isJavaScriptFunction: true }
        Method { name: "setName"; isJavaScriptFunction: true }
        Method { name: "setVersion"; isJavaScriptFunction: true }
        Method { name: "setOrganization"; isJavaScriptFunction: true }
        Method { name: "setDomain"; isJavaScriptFunction: true }
    }
    Component {
        name: "QtApplicationArguments"
        accessSemantics: "reference"
        prototype: "QtApplicationArgumentsPrototype"
        isJavaScriptBuiltin: true
        Property { name: "0" }
        Property { name: "1" }
        Property { name: "length"; type: "number" }
    }
    Component {
        name: "QtApplicationArgumentsPrototype"
        accessSemantics: "reference"
        prototype: "ArrayPrototype"
        isJavaScriptBuiltin: true
        Property { name: "length" }
        Method { name: "valueOf"; isJavaScriptFunction: true }
        Method { name: "shift"; isJavaScriptFunction: true }
    }
    Component {
        name: "QtInputMethod"
        accessSemantics: "reference"
        prototype: "ObjectPrototype"
        isJavaScriptBuiltin: true
        Property { name: "objectName"; type: "string" }
        Method { name: "objectNameChanged"; isJavaScriptFunction: true }
    }
    Component {
        name: "QtPlatform"
        accessSemantics: "reference"
        prototype: "ObjectPrototype"
        isJavaScriptBuiltin: true
        Property { name: "objectName"; type: "string" }
        Property { name: "os"; type: "string" }
        Property { name: "pluginName"; type: "string" }
        Method { name: "objectNameChanged"; isJavaScriptFunction: true }
    }
    Component {
        name: "QtPrototype"
        accessSemantics: "reference"
        prototype: "ObjectPrototype"
        isJavaScriptBuiltin: true
        Property { name: "color0"; type: "number"; isReadonly: true }
        Property { name: "color1"; type: "number"; isReadonly: true }
        Property { name: "black"; type: "number"; isReadonly: true }
        Property { name: "white"; type: "number"; isReadonly: true }
        Property { name: "darkGray"; type: "number"; isReadonly: true }
        Property { name: "gray"; type: "number"; isReadonly: true }
        Property { name: "lightGray"; type: "number"; isReadonly: true }
        Property { name: "red"; type: "number"; isReadonly: true }
        Property { name: "green"; type: "number"; isReadonly: true }
        Property { name: "blue"; type: "number"; isReadonly: true }
        Property { name: "cyan"; type: "number"; isReadonly: true }
        Property { name: "magenta"; type: "number"; isReadonly: true }
        Property { name: "yellow"; type: "number"; isReadonly: true }
        Property { name: "darkRed"; type: "number"; isReadonly: true }
        Property { name: "darkGreen"; type: "number"; isReadonly: true }
        Property { name: "darkBlue"; type: "number"; isReadonly: true }
        Property { name: "darkCyan"; type: "number"; isReadonly: true }
        Property { name: "darkMagenta"; type: "number"; isReadonly: true }
        Property { name: "darkYellow"; type: "number"; isReadonly: true }
        Property { name: "transparent"; type: "number"; isReadonly: true }
        Property { name: "Unknown"; type: "number"; isReadonly: true }
        Property { name: "Light"; type: "number"; isReadonly: true }
        Property { name: "Dark"; type: "number"; isReadonly: true }
        Property { name: "NoButton"; type: "number"; isReadonly: true }
        Property { name: "LeftButton"; type: "number"; isReadonly: true }
        Property { name: "RightButton"; type: "number"; isReadonly: true }
        Property { name: "MiddleButton"; type: "number"; isReadonly: true }
        Property { name: "BackButton"; type: "number"; isReadonly: true }
        Property { name: "XButton1"; type: "number"; isReadonly: true }
        Property { name: "ExtraButton1"; type: "number"; isReadonly: true }
        Property { name: "ForwardButton"; type: "number"; isReadonly: true }
        Property { name: "XButton2"; type: "number"; isReadonly: true }
        Property { name: "ExtraButton2"; type: "number"; isReadonly: true }
        Property { name: "TaskButton"; type: "number"; isReadonly: true }
        Property { name: "ExtraButton3"; type: "number"; isReadonly: true }
        Property { name: "ExtraButton4"; type: "number"; isReadonly: true }
        Property { name: "ExtraButton5"; type: "number"; isReadonly: true }
        Property { name: "ExtraButton6"; type: "number"; isReadonly: true }
        Property { name: "ExtraButton7"; type: "number"; isReadonly: true }
        Property { name: "ExtraButton8"; type: "number"; isReadonly: true }
        Property { name: "ExtraButton9"; type: "number"; isReadonly: true }
        Property { name: "ExtraButton10"; type: "number"; isReadonly: true }
        Property { name: "ExtraButton11"; type: "number"; isReadonly: true }
        Property { name: "ExtraButton12"; type: "number"; isReadonly: true }
        Property { name: "ExtraButton13"; type: "number"; isReadonly: true }
        Property { name: "ExtraButton14"; type: "number"; isReadonly: true }
        Property { name: "ExtraButton15"; type: "number"; isReadonly: true }
        Property { name: "ExtraButton16"; type: "number"; isReadonly: true }
        Property { name: "ExtraButton17"; type: "number"; isReadonly: true }
        Property { name: "ExtraButton18"; type: "number"; isReadonly: true }
        Property { name: "ExtraButton19"; type: "number"; isReadonly: true }
        Property { name: "ExtraButton20"; type: "number"; isReadonly: true }
        Property { name: "ExtraButton21"; type: "number"; isReadonly: true }
        Property { name: "ExtraButton22"; type: "number"; isReadonly: true }
        Property { name: "ExtraButton23"; type: "number"; isReadonly: true }
        Property { name: "ExtraButton24"; type: "number"; isReadonly: true }
        Property { name: "AllButtons"; type: "number"; isReadonly: true }
        Property { name: "MaxMouseButton"; type: "number"; isReadonly: true }
        Property { name: "MouseButtonMask"; type: "number"; isReadonly: true }
        Property { name: "Horizontal"; type: "number"; isReadonly: true }
        Property { name: "Vertical"; type: "number"; isReadonly: true }
        Property { name: "NoFocus"; type: "number"; isReadonly: true }
        Property { name: "TabFocus"; type: "number"; isReadonly: true }
        Property { name: "ClickFocus"; type: "number"; isReadonly: true }
        Property { name: "StrongFocus"; type: "number"; isReadonly: true }
        Property { name: "WheelFocus"; type: "number"; isReadonly: true }
        Property { name: "NoTabFocus"; type: "number"; isReadonly: true }
        Property { name: "TabFocusTextControls"; type: "number"; isReadonly: true }
        Property { name: "TabFocusListControls"; type: "number"; isReadonly: true }
        Property { name: "TabFocusAllControls"; type: "number"; isReadonly: true }
        Property { name: "AscendingOrder"; type: "number"; isReadonly: true }
        Property { name: "DescendingOrder"; type: "number"; isReadonly: true }
        Property { name: "KeepEmptyParts"; type: "number"; isReadonly: true }
        Property { name: "SkipEmptyParts"; type: "number"; isReadonly: true }
        Property { name: "AlignLeft"; type: "number"; isReadonly: true }
        Property { name: "AlignLeading"; type: "number"; isReadonly: true }
        Property { name: "AlignRight"; type: "number"; isReadonly: true }
        Property { name: "AlignTrailing"; type: "number"; isReadonly: true }
        Property { name: "AlignHCenter"; type: "number"; isReadonly: true }
        Property { name: "AlignJustify"; type: "number"; isReadonly: true }
        Property { name: "AlignAbsolute"; type: "number"; isReadonly: true }
        Property { name: "AlignHorizontal_Mask"; type: "number"; isReadonly: true }
        Property { name: "AlignTop"; type: "number"; isReadonly: true }
        Property { name: "AlignBottom"; type: "number"; isReadonly: true }
        Property { name: "AlignVCenter"; type: "number"; isReadonly: true }
        Property { name: "AlignBaseline"; type: "number"; isReadonly: true }
        Property { name: "AlignVertical_Mask"; type: "number"; isReadonly: true }
        Property { name: "AlignCenter"; type: "number"; isReadonly: true }
        Property { name: "TextSingleLine"; type: "number"; isReadonly: true }
        Property { name: "TextDontClip"; type: "number"; isReadonly: true }
        Property { name: "TextExpandTabs"; type: "number"; isReadonly: true }
        Property { name: "TextShowMnemonic"; type: "number"; isReadonly: true }
        Property { name: "TextWordWrap"; type: "number"; isReadonly: true }
        Property { name: "TextWrapAnywhere"; type: "number"; isReadonly: true }
        Property { name: "TextDontPrint"; type: "number"; isReadonly: true }
        Property { name: "TextIncludeTrailingSpaces"; type: "number"; isReadonly: true }
        Property { name: "TextHideMnemonic"; type: "number"; isReadonly: true }
        Property { name: "TextJustificationForced"; type: "number"; isReadonly: true }
        Property { name: "TextForceLeftToRight"; type: "number"; isReadonly: true }
        Property { name: "TextForceRightToLeft"; type: "number"; isReadonly: true }
        Property { name: "TextLongestVariant"; type: "number"; isReadonly: true }
        Property { name: "ElideLeft"; type: "number"; isReadonly: true }
        Property { name: "ElideRight"; type: "number"; isReadonly: true }
        Property { name: "ElideMiddle"; type: "number"; isReadonly: true }
        Property { name: "ElideNone"; type: "number"; isReadonly: true }
        Property { name: "Widget"; type: "number"; isReadonly: true }
        Property { name: "Window"; type: "number"; isReadonly: true }
        Property { name: "Dialog"; type: "number"; isReadonly: true }
        Property { name: "Sheet"; type: "number"; isReadonly: true }
        Property { name: "Drawer"; type: "number"; isReadonly: true }
        Property { name: "Popup"; type: "number"; isReadonly: true }
        Property { name: "Tool"; type: "number"; isReadonly: true }
        Property { name: "ToolTip"; type: "number"; isReadonly: true }
        Property { name: "SplashScreen"; type: "number"; isReadonly: true }
        Property { name: "Desktop"; type: "number"; isReadonly: true }
        Property { name: "SubWindow"; type: "number"; isReadonly: true }
        Property { name: "ForeignWindow"; type: "number"; isReadonly: true }
        Property { name: "CoverWindow"; type: "number"; isReadonly: true }
        Property { name: "WindowType_Mask"; type: "number"; isReadonly: true }
        Property { name: "MSWindowsFixedSizeDialogHint"; type: "number"; isReadonly: true }
        Property { name: "MSWindowsOwnDC"; type: "number"; isReadonly: true }
        Property { name: "BypassWindowManagerHint"; type: "number"; isReadonly: true }
        Property { name: "X11BypassWindowManagerHint"; type: "number"; isReadonly: true }
        Property { name: "FramelessWindowHint"; type: "number"; isReadonly: true }
        Property { name: "WindowTitleHint"; type: "number"; isReadonly: true }
        Property { name: "WindowSystemMenuHint"; type: "number"; isReadonly: true }
        Property { name: "WindowMinimizeButtonHint"; type: "number"; isReadonly: true }
        Property { name: "WindowMaximizeButtonHint"; type: "number"; isReadonly: true }
        Property { name: "WindowMinMaxButtonsHint"; type: "number"; isReadonly: true }
        Property { name: "WindowContextHelpButtonHint"; type: "number"; isReadonly: true }
        Property { name: "WindowShadeButtonHint"; type: "number"; isReadonly: true }
        Property { name: "WindowStaysOnTopHint"; type: "number"; isReadonly: true }
        Property { name: "WindowTransparentForInput"; type: "number"; isReadonly: true }
        Property { name: "WindowOverridesSystemGestures"; type: "number"; isReadonly: true }
        Property { name: "WindowDoesNotAcceptFocus"; type: "number"; isReadonly: true }
        Property { name: "MaximizeUsingFullscreenGeometryHint"; type: "number"; isReadonly: true }
        Property { name: "ExpandedClientAreaHint"; type: "number"; isReadonly: true }
        Property { name: "NoTitleBarBackgroundHint"; type: "number"; isReadonly: true }
        Property { name: "CustomizeWindowHint"; type: "number"; isReadonly: true }
        Property { name: "WindowStaysOnBottomHint"; type: "number"; isReadonly: true }
        Property { name: "WindowCloseButtonHint"; type: "number"; isReadonly: true }
        Property { name: "MacWindowToolBarButtonHint"; type: "number"; isReadonly: true }
        Property { name: "BypassGraphicsProxyWidget"; type: "number"; isReadonly: true }
        Property { name: "NoDropShadowWindowHint"; type: "number"; isReadonly: true }
        Property { name: "WindowFullscreenButtonHint"; type: "number"; isReadonly: true }
        Property { name: "WindowNoState"; type: "number"; isReadonly: true }
        Property { name: "WindowMinimized"; type: "number"; isReadonly: true }
        Property { name: "WindowMaximized"; type: "number"; isReadonly: true }
        Property { name: "WindowFullScreen"; type: "number"; isReadonly: true }
        Property { name: "WindowActive"; type: "number"; isReadonly: true }
        Property { name: "ApplicationSuspended"; type: "number"; isReadonly: true }
        Property { name: "ApplicationHidden"; type: "number"; isReadonly: true }
        Property { name: "ApplicationInactive"; type: "number"; isReadonly: true }
        Property { name: "ApplicationActive"; type: "number"; isReadonly: true }
        Property { name: "PrimaryOrientation"; type: "number"; isReadonly: true }
        Property { name: "PortraitOrientation"; type: "number"; isReadonly: true }
        Property { name: "LandscapeOrientation"; type: "number"; isReadonly: true }
        Property { name: "InvertedPortraitOrientation"; type: "number"; isReadonly: true }
        Property { name: "InvertedLandscapeOrientation"; type: "number"; isReadonly: true }
        Property { name: "WA_Disabled"; type: "number"; isReadonly: true }
        Property { name: "WA_UnderMouse"; type: "number"; isReadonly: true }
        Property { name: "WA_MouseTracking"; type: "number"; isReadonly: true }
        Property { name: "WA_OpaquePaintEvent"; type: "number"; isReadonly: true }
        Property { name: "WA_StaticContents"; type: "number"; isReadonly: true }
        Property { name: "WA_LaidOut"; type: "number"; isReadonly: true }
        Property { name: "WA_PaintOnScreen"; type: "number"; isReadonly: true }
        Property { name: "WA_NoSystemBackground"; type: "number"; isReadonly: true }
        Property { name: "WA_UpdatesDisabled"; type: "number"; isReadonly: true }
        Property { name: "WA_Mapped"; type: "number"; isReadonly: true }
        Property { name: "WA_InputMethodEnabled"; type: "number"; isReadonly: true }
        Property { name: "WA_WState_Visible"; type: "number"; isReadonly: true }
        Property { name: "WA_WState_Hidden"; type: "number"; isReadonly: true }
        Property { name: "WA_ForceDisabled"; type: "number"; isReadonly: true }
        Property { name: "WA_KeyCompression"; type: "number"; isReadonly: true }
        Property { name: "WA_PendingMoveEvent"; type: "number"; isReadonly: true }
        Property { name: "WA_PendingResizeEvent"; type: "number"; isReadonly: true }
        Property { name: "WA_SetPalette"; type: "number"; isReadonly: true }
        Property { name: "WA_SetFont"; type: "number"; isReadonly: true }
        Property { name: "WA_SetCursor"; type: "number"; isReadonly: true }
        Property { name: "WA_NoChildEventsFromChildren"; type: "number"; isReadonly: true }
        Property { name: "WA_WindowModified"; type: "number"; isReadonly: true }
        Property { name: "WA_Resized"; type: "number"; isReadonly: true }
        Property { name: "WA_Moved"; type: "number"; isReadonly: true }
        Property { name: "WA_PendingUpdate"; type: "number"; isReadonly: true }
        Property { name: "WA_InvalidSize"; type: "number"; isReadonly: true }
        Property { name: "WA_CustomWhatsThis"; type: "number"; isReadonly: true }
        Property { name: "WA_LayoutOnEntireRect"; type: "number"; isReadonly: true }
        Property { name: "WA_OutsideWSRange"; type: "number"; isReadonly: true }
        Property { name: "WA_GrabbedShortcut"; type: "number"; isReadonly: true }
        Property { name: "WA_TransparentForMouseEvents"; type: "number"; isReadonly: true }
        Property { name: "WA_PaintUnclipped"; type: "number"; isReadonly: true }
        Property { name: "WA_SetWindowIcon"; type: "number"; isReadonly: true }
        Property { name: "WA_NoMouseReplay"; type: "number"; isReadonly: true }
        Property { name: "WA_DeleteOnClose"; type: "number"; isReadonly: true }
        Property { name: "WA_RightToLeft"; type: "number"; isReadonly: true }
        Property { name: "WA_SetLayoutDirection"; type: "number"; isReadonly: true }
        Property { name: "WA_NoChildEventsForParent"; type: "number"; isReadonly: true }
        Property { name: "WA_ForceUpdatesDisabled"; type: "number"; isReadonly: true }
        Property { name: "WA_WState_Created"; type: "number"; isReadonly: true }
        Property { name: "WA_WState_CompressKeys"; type: "number"; isReadonly: true }
        Property { name: "WA_WState_InPaintEvent"; type: "number"; isReadonly: true }
        Property { name: "WA_WState_Reparented"; type: "number"; isReadonly: true }
        Property { name: "WA_WState_ConfigPending"; type: "number"; isReadonly: true }
        Property { name: "WA_WState_Polished"; type: "number"; isReadonly: true }
        Property { name: "WA_WState_OwnSizePolicy"; type: "number"; isReadonly: true }
        Property { name: "WA_WState_ExplicitShowHide"; type: "number"; isReadonly: true }
        Property { name: "WA_ShowModal"; type: "number"; isReadonly: true }
        Property { name: "WA_MouseNoMask"; type: "number"; isReadonly: true }
        Property { name: "WA_NoMousePropagation"; type: "number"; isReadonly: true }
        Property { name: "WA_Hover"; type: "number"; isReadonly: true }
        Property { name: "WA_InputMethodTransparent"; type: "number"; isReadonly: true }
        Property { name: "WA_QuitOnClose"; type: "number"; isReadonly: true }
        Property { name: "WA_KeyboardFocusChange"; type: "number"; isReadonly: true }
        Property { name: "WA_AcceptDrops"; type: "number"; isReadonly: true }
        Property { name: "WA_DropSiteRegistered"; type: "number"; isReadonly: true }
        Property { name: "WA_WindowPropagation"; type: "number"; isReadonly: true }
        Property { name: "WA_NoX11EventCompression"; type: "number"; isReadonly: true }
        Property { name: "WA_TintedBackground"; type: "number"; isReadonly: true }
        Property { name: "WA_X11OpenGLOverlay"; type: "number"; isReadonly: true }
        Property { name: "WA_AlwaysShowToolTips"; type: "number"; isReadonly: true }
        Property { name: "WA_MacOpaqueSizeGrip"; type: "number"; isReadonly: true }
        Property { name: "WA_SetStyle"; type: "number"; isReadonly: true }
        Property { name: "WA_SetLocale"; type: "number"; isReadonly: true }
        Property { name: "WA_MacShowFocusRect"; type: "number"; isReadonly: true }
        Property { name: "WA_MacNormalSize"; type: "number"; isReadonly: true }
        Property { name: "WA_MacSmallSize"; type: "number"; isReadonly: true }
        Property { name: "WA_MacMiniSize"; type: "number"; isReadonly: true }
        Property { name: "WA_LayoutUsesWidgetRect"; type: "number"; isReadonly: true }
        Property { name: "WA_StyledBackground"; type: "number"; isReadonly: true }
        Property { name: "WA_CanHostQMdiSubWindowTitleBar"; type: "number"; isReadonly: true }
        Property { name: "WA_MacAlwaysShowToolWindow"; type: "number"; isReadonly: true }
        Property { name: "WA_StyleSheet"; type: "number"; isReadonly: true }
        Property { name: "WA_ShowWithoutActivating"; type: "number"; isReadonly: true }
        Property { name: "WA_X11BypassTransientForHint"; type: "number"; isReadonly: true }
        Property { name: "WA_NativeWindow"; type: "number"; isReadonly: true }
        Property { name: "WA_DontCreateNativeAncestors"; type: "number"; isReadonly: true }
        Property { name: "WA_DontShowOnScreen"; type: "number"; isReadonly: true }
        Property { name: "WA_X11NetWmWindowTypeDesktop"; type: "number"; isReadonly: true }
        Property { name: "WA_X11NetWmWindowTypeDock"; type: "number"; isReadonly: true }
        Property { name: "WA_X11NetWmWindowTypeToolBar"; type: "number"; isReadonly: true }
        Property { name: "WA_X11NetWmWindowTypeMenu"; type: "number"; isReadonly: true }
        Property { name: "WA_X11NetWmWindowTypeUtility"; type: "number"; isReadonly: true }
        Property { name: "WA_X11NetWmWindowTypeSplash"; type: "number"; isReadonly: true }
        Property { name: "WA_X11NetWmWindowTypeDialog"; type: "number"; isReadonly: true }
        Property { name: "WA_X11NetWmWindowTypeDropDownMenu"; type: "number"; isReadonly: true }
        Property { name: "WA_X11NetWmWindowTypePopupMenu"; type: "number"; isReadonly: true }
        Property { name: "WA_X11NetWmWindowTypeToolTip"; type: "number"; isReadonly: true }
        Property { name: "WA_X11NetWmWindowTypeNotification"; type: "number"; isReadonly: true }
        Property { name: "WA_X11NetWmWindowTypeCombo"; type: "number"; isReadonly: true }
        Property { name: "WA_X11NetWmWindowTypeDND"; type: "number"; isReadonly: true }
        Property { name: "WA_SetWindowModality"; type: "number"; isReadonly: true }
        Property { name: "WA_WState_WindowOpacitySet"; type: "number"; isReadonly: true }
        Property { name: "WA_TranslucentBackground"; type: "number"; isReadonly: true }
        Property { name: "WA_AcceptTouchEvents"; type: "number"; isReadonly: true }
        Property { name: "WA_WState_AcceptedTouchBeginEvent"; type: "number"; isReadonly: true }
        Property { name: "WA_TouchPadAcceptSingleTouchEvents"; type: "number"; isReadonly: true }
        Property { name: "WA_X11DoNotAcceptFocus"; type: "number"; isReadonly: true }
        Property { name: "WA_AlwaysStackOnTop"; type: "number"; isReadonly: true }
        Property { name: "WA_TabletTracking"; type: "number"; isReadonly: true }
        Property { name: "WA_ContentsMarginsRespectsSafeArea"; type: "number"; isReadonly: true }
        Property { name: "WA_StyleSheetTarget"; type: "number"; isReadonly: true }
        Property { name: "WA_AttributeCount"; type: "number"; isReadonly: true }
        Property { name: "AA_QtQuickUseDefaultSizePolicy"; type: "number"; isReadonly: true }
        Property { name: "AA_DontShowIconsInMenus"; type: "number"; isReadonly: true }
        Property { name: "AA_NativeWindows"; type: "number"; isReadonly: true }
        Property { name: "AA_DontCreateNativeWidgetSiblings"; type: "number"; isReadonly: true }
        Property { name: "AA_PluginApplication"; type: "number"; isReadonly: true }
        Property { name: "AA_DontUseNativeMenuBar"; type: "number"; isReadonly: true }
        Property { name: "AA_MacDontSwapCtrlAndMeta"; type: "number"; isReadonly: true }
        Property { name: "AA_Use96Dpi"; type: "number"; isReadonly: true }
        Property { name: "AA_DisableNativeVirtualKeyboard"; type: "number"; isReadonly: true }
        Property { name: "AA_DontUseNativeMenuWindows"; type: "number"; isReadonly: true }
        Property { name: "AA_SynthesizeTouchForUnhandledMouseEvents"; type: "number"; isReadonly: true }
        Property { name: "AA_SynthesizeMouseForUnhandledTouchEvents"; type: "number"; isReadonly: true }
        Property { name: "AA_UseHighDpiPixmaps"; type: "number"; isReadonly: true }
        Property { name: "AA_ForceRasterWidgets"; type: "number"; isReadonly: true }
        Property { name: "AA_UseDesktopOpenGL"; type: "number"; isReadonly: true }
        Property { name: "AA_UseOpenGLES"; type: "number"; isReadonly: true }
        Property { name: "AA_UseSoftwareOpenGL"; type: "number"; isReadonly: true }
        Property { name: "AA_ShareOpenGLContexts"; type: "number"; isReadonly: true }
        Property { name: "AA_SetPalette"; type: "number"; isReadonly: true }
        Property { name: "AA_EnableHighDpiScaling"; type: "number"; isReadonly: true }
        Property { name: "AA_DisableHighDpiScaling"; type: "number"; isReadonly: true }
        Property { name: "AA_UseStyleSheetPropagationInWidgetStyles"; type: "number"; isReadonly: true }
        Property { name: "AA_DontUseNativeDialogs"; type: "number"; isReadonly: true }
        Property {
            name: "AA_SynthesizeMouseForUnhandledTabletEvents"
            type: "number"
            isReadonly: true
        }
        Property { name: "AA_CompressHighFrequencyEvents"; type: "number"; isReadonly: true }
        Property { name: "AA_DontCheckOpenGLContextThreadAffinity"; type: "number"; isReadonly: true }
        Property { name: "AA_DisableShaderDiskCache"; type: "number"; isReadonly: true }
        Property { name: "AA_DontShowShortcutsInContextMenus"; type: "number"; isReadonly: true }
        Property { name: "AA_CompressTabletEvents"; type: "number"; isReadonly: true }
        Property { name: "AA_DisableSessionManager"; type: "number"; isReadonly: true }
        Property { name: "AA_AttributeCount"; type: "number"; isReadonly: true }
        Property { name: "ColorMode_Mask"; type: "number"; isReadonly: true }
        Property { name: "AutoColor"; type: "number"; isReadonly: true }
        Property { name: "ColorOnly"; type: "number"; isReadonly: true }
        Property { name: "MonoOnly"; type: "number"; isReadonly: true }
        Property { name: "AlphaDither_Mask"; type: "number"; isReadonly: true }
        Property { name: "ThresholdAlphaDither"; type: "number"; isReadonly: true }
        Property { name: "OrderedAlphaDither"; type: "number"; isReadonly: true }
        Property { name: "DiffuseAlphaDither"; type: "number"; isReadonly: true }
        Property { name: "NoAlpha"; type: "number"; isReadonly: true }
        Property { name: "Dither_Mask"; type: "number"; isReadonly: true }
        Property { name: "DiffuseDither"; type: "number"; isReadonly: true }
        Property { name: "OrderedDither"; type: "number"; isReadonly: true }
        Property { name: "ThresholdDither"; type: "number"; isReadonly: true }
        Property { name: "DitherMode_Mask"; type: "number"; isReadonly: true }
        Property { name: "AutoDither"; type: "number"; isReadonly: true }
        Property { name: "PreferDither"; type: "number"; isReadonly: true }
        Property { name: "AvoidDither"; type: "number"; isReadonly: true }
        Property { name: "NoOpaqueDetection"; type: "number"; isReadonly: true }
        Property { name: "NoFormatConversion"; type: "number"; isReadonly: true }
        Property { name: "TransparentMode"; type: "number"; isReadonly: true }
        Property { name: "OpaqueMode"; type: "number"; isReadonly: true }
        Property { name: "Key_Space"; type: "number"; isReadonly: true }
        Property { name: "Key_Any"; type: "number"; isReadonly: true }
        Property { name: "Key_Exclam"; type: "number"; isReadonly: true }
        Property { name: "Key_QuoteDbl"; type: "number"; isReadonly: true }
        Property { name: "Key_NumberSign"; type: "number"; isReadonly: true }
        Property { name: "Key_Dollar"; type: "number"; isReadonly: true }
        Property { name: "Key_Percent"; type: "number"; isReadonly: true }
        Property { name: "Key_Ampersand"; type: "number"; isReadonly: true }
        Property { name: "Key_Apostrophe"; type: "number"; isReadonly: true }
        Property { name: "Key_ParenLeft"; type: "number"; isReadonly: true }
        Property { name: "Key_ParenRight"; type: "number"; isReadonly: true }
        Property { name: "Key_Asterisk"; type: "number"; isReadonly: true }
        Property { name: "Key_Plus"; type: "number"; isReadonly: true }
        Property { name: "Key_Comma"; type: "number"; isReadonly: true }
        Property { name: "Key_Minus"; type: "number"; isReadonly: true }
        Property { name: "Key_Period"; type: "number"; isReadonly: true }
        Property { name: "Key_Slash"; type: "number"; isReadonly: true }
        Property { name: "Key_0"; type: "number"; isReadonly: true }
        Property { name: "Key_1"; type: "number"; isReadonly: true }
        Property { name: "Key_2"; type: "number"; isReadonly: true }
        Property { name: "Key_3"; type: "number"; isReadonly: true }
        Property { name: "Key_4"; type: "number"; isReadonly: true }
        Property { name: "Key_5"; type: "number"; isReadonly: true }
        Property { name: "Key_6"; type: "number"; isReadonly: true }
        Property { name: "Key_7"; type: "number"; isReadonly: true }
        Property { name: "Key_8"; type: "number"; isReadonly: true }
        Property { name: "Key_9"; type: "number"; isReadonly: true }
        Property { name: "Key_Colon"; type: "number"; isReadonly: true }
        Property { name: "Key_Semicolon"; type: "number"; isReadonly: true }
        Property { name: "Key_Less"; type: "number"; isReadonly: true }
        Property { name: "Key_Equal"; type: "number"; isReadonly: true }
        Property { name: "Key_Greater"; type: "number"; isReadonly: true }
        Property { name: "Key_Question"; type: "number"; isReadonly: true }
        Property { name: "Key_At"; type: "number"; isReadonly: true }
        Property { name: "Key_A"; type: "number"; isReadonly: true }
        Property { name: "Key_B"; type: "number"; isReadonly: true }
        Property { name: "Key_C"; type: "number"; isReadonly: true }
        Property { name: "Key_D"; type: "number"; isReadonly: true }
        Property { name: "Key_E"; type: "number"; isReadonly: true }
        Property { name: "Key_F"; type: "number"; isReadonly: true }
        Property { name: "Key_G"; type: "number"; isReadonly: true }
        Property { name: "Key_H"; type: "number"; isReadonly: true }
        Property { name: "Key_I"; type: "number"; isReadonly: true }
        Property { name: "Key_J"; type: "number"; isReadonly: true }
        Property { name: "Key_K"; type: "number"; isReadonly: true }
        Property { name: "Key_L"; type: "number"; isReadonly: true }
        Property { name: "Key_M"; type: "number"; isReadonly: true }
        Property { name: "Key_N"; type: "number"; isReadonly: true }
        Property { name: "Key_O"; type: "number"; isReadonly: true }
        Property { name: "Key_P"; type: "number"; isReadonly: true }
        Property { name: "Key_Q"; type: "number"; isReadonly: true }
        Property { name: "Key_R"; type: "number"; isReadonly: true }
        Property { name: "Key_S"; type: "number"; isReadonly: true }
        Property { name: "Key_T"; type: "number"; isReadonly: true }
        Property { name: "Key_U"; type: "number"; isReadonly: true }
        Property { name: "Key_V"; type: "number"; isReadonly: true }
        Property { name: "Key_W"; type: "number"; isReadonly: true }
        Property { name: "Key_X"; type: "number"; isReadonly: true }
        Property { name: "Key_Y"; type: "number"; isReadonly: true }
        Property { name: "Key_Z"; type: "number"; isReadonly: true }
        Property { name: "Key_BracketLeft"; type: "number"; isReadonly: true }
        Property { name: "Key_Backslash"; type: "number"; isReadonly: true }
        Property { name: "Key_BracketRight"; type: "number"; isReadonly: true }
        Property { name: "Key_AsciiCircum"; type: "number"; isReadonly: true }
        Property { name: "Key_Underscore"; type: "number"; isReadonly: true }
        Property { name: "Key_QuoteLeft"; type: "number"; isReadonly: true }
        Property { name: "Key_BraceLeft"; type: "number"; isReadonly: true }
        Property { name: "Key_Bar"; type: "number"; isReadonly: true }
        Property { name: "Key_BraceRight"; type: "number"; isReadonly: true }
        Property { name: "Key_AsciiTilde"; type: "number"; isReadonly: true }
        Property { name: "Key_nobreakspace"; type: "number"; isReadonly: true }
        Property { name: "Key_exclamdown"; type: "number"; isReadonly: true }
        Property { name: "Key_cent"; type: "number"; isReadonly: true }
        Property { name: "Key_sterling"; type: "number"; isReadonly: true }
        Property { name: "Key_currency"; type: "number"; isReadonly: true }
        Property { name: "Key_yen"; type: "number"; isReadonly: true }
        Property { name: "Key_brokenbar"; type: "number"; isReadonly: true }
        Property { name: "Key_section"; type: "number"; isReadonly: true }
        Property { name: "Key_diaeresis"; type: "number"; isReadonly: true }
        Property { name: "Key_copyright"; type: "number"; isReadonly: true }
        Property { name: "Key_ordfeminine"; type: "number"; isReadonly: true }
        Property { name: "Key_guillemotleft"; type: "number"; isReadonly: true }
        Property { name: "Key_notsign"; type: "number"; isReadonly: true }
        Property { name: "Key_hyphen"; type: "number"; isReadonly: true }
        Property { name: "Key_registered"; type: "number"; isReadonly: true }
        Property { name: "Key_macron"; type: "number"; isReadonly: true }
        Property { name: "Key_degree"; type: "number"; isReadonly: true }
        Property { name: "Key_plusminus"; type: "number"; isReadonly: true }
        Property { name: "Key_twosuperior"; type: "number"; isReadonly: true }
        Property { name: "Key_threesuperior"; type: "number"; isReadonly: true }
        Property { name: "Key_acute"; type: "number"; isReadonly: true }
        Property { name: "Key_micro"; type: "number"; isReadonly: true }
        Property { name: "Key_mu"; type: "number"; isReadonly: true }
        Property { name: "Key_paragraph"; type: "number"; isReadonly: true }
        Property { name: "Key_periodcentered"; type: "number"; isReadonly: true }
        Property { name: "Key_cedilla"; type: "number"; isReadonly: true }
        Property { name: "Key_onesuperior"; type: "number"; isReadonly: true }
        Property { name: "Key_masculine"; type: "number"; isReadonly: true }
        Property { name: "Key_guillemotright"; type: "number"; isReadonly: true }
        Property { name: "Key_onequarter"; type: "number"; isReadonly: true }
        Property { name: "Key_onehalf"; type: "number"; isReadonly: true }
        Property { name: "Key_threequarters"; type: "number"; isReadonly: true }
        Property { name: "Key_questiondown"; type: "number"; isReadonly: true }
        Property { name: "Key_Agrave"; type: "number"; isReadonly: true }
        Property { name: "Key_Aacute"; type: "number"; isReadonly: true }
        Property { name: "Key_Acircumflex"; type: "number"; isReadonly: true }
        Property { name: "Key_Atilde"; type: "number"; isReadonly: true }
        Property { name: "Key_Adiaeresis"; type: "number"; isReadonly: true }
        Property { name: "Key_Aring"; type: "number"; isReadonly: true }
        Property { name: "Key_AE"; type: "number"; isReadonly: true }
        Property { name: "Key_Ccedilla"; type: "number"; isReadonly: true }
        Property { name: "Key_Egrave"; type: "number"; isReadonly: true }
        Property { name: "Key_Eacute"; type: "number"; isReadonly: true }
        Property { name: "Key_Ecircumflex"; type: "number"; isReadonly: true }
        Property { name: "Key_Ediaeresis"; type: "number"; isReadonly: true }
        Property { name: "Key_Igrave"; type: "number"; isReadonly: true }
        Property { name: "Key_Iacute"; type: "number"; isReadonly: true }
        Property { name: "Key_Icircumflex"; type: "number"; isReadonly: true }
        Property { name: "Key_Idiaeresis"; type: "number"; isReadonly: true }
        Property { name: "Key_ETH"; type: "number"; isReadonly: true }
        Property { name: "Key_Ntilde"; type: "number"; isReadonly: true }
        Property { name: "Key_Ograve"; type: "number"; isReadonly: true }
        Property { name: "Key_Oacute"; type: "number"; isReadonly: true }
        Property { name: "Key_Ocircumflex"; type: "number"; isReadonly: true }
        Property { name: "Key_Otilde"; type: "number"; isReadonly: true }
        Property { name: "Key_Odiaeresis"; type: "number"; isReadonly: true }
        Property { name: "Key_multiply"; type: "number"; isReadonly: true }
        Property { name: "Key_Ooblique"; type: "number"; isReadonly: true }
        Property { name: "Key_Ugrave"; type: "number"; isReadonly: true }
        Property { name: "Key_Uacute"; type: "number"; isReadonly: true }
        Property { name: "Key_Ucircumflex"; type: "number"; isReadonly: true }
        Property { name: "Key_Udiaeresis"; type: "number"; isReadonly: true }
        Property { name: "Key_Yacute"; type: "number"; isReadonly: true }
        Property { name: "Key_THORN"; type: "number"; isReadonly: true }
        Property { name: "Key_ssharp"; type: "number"; isReadonly: true }
        Property { name: "Key_division"; type: "number"; isReadonly: true }
        Property { name: "Key_ydiaeresis"; type: "number"; isReadonly: true }
        Property { name: "Key_Escape"; type: "number"; isReadonly: true }
        Property { name: "Key_Tab"; type: "number"; isReadonly: true }
        Property { name: "Key_Backtab"; type: "number"; isReadonly: true }
        Property { name: "Key_Backspace"; type: "number"; isReadonly: true }
        Property { name: "Key_Return"; type: "number"; isReadonly: true }
        Property { name: "Key_Enter"; type: "number"; isReadonly: true }
        Property { name: "Key_Insert"; type: "number"; isReadonly: true }
        Property { name: "Key_Delete"; type: "number"; isReadonly: true }
        Property { name: "Key_Pause"; type: "number"; isReadonly: true }
        Property { name: "Key_Print"; type: "number"; isReadonly: true }
        Property { name: "Key_SysReq"; type: "number"; isReadonly: true }
        Property { name: "Key_Clear"; type: "number"; isReadonly: true }
        Property { name: "Key_Home"; type: "number"; isReadonly: true }
        Property { name: "Key_End"; type: "number"; isReadonly: true }
        Property { name: "Key_Left"; type: "number"; isReadonly: true }
        Property { name: "Key_Up"; type: "number"; isReadonly: true }
        Property { name: "Key_Right"; type: "number"; isReadonly: true }
        Property { name: "Key_Down"; type: "number"; isReadonly: true }
        Property { name: "Key_PageUp"; type: "number"; isReadonly: true }
        Property { name: "Key_PageDown"; type: "number"; isReadonly: true }
        Property { name: "Key_Shift"; type: "number"; isReadonly: true }
        Property { name: "Key_Control"; type: "number"; isReadonly: true }
        Property { name: "Key_Meta"; type: "number"; isReadonly: true }
        Property { name: "Key_Alt"; type: "number"; isReadonly: true }
        Property { name: "Key_CapsLock"; type: "number"; isReadonly: true }
        Property { name: "Key_NumLock"; type: "number"; isReadonly: true }
        Property { name: "Key_ScrollLock"; type: "number"; isReadonly: true }
        Property { name: "Key_F1"; type: "number"; isReadonly: true }
        Property { name: "Key_F2"; type: "number"; isReadonly: true }
        Property { name: "Key_F3"; type: "number"; isReadonly: true }
        Property { name: "Key_F4"; type: "number"; isReadonly: true }
        Property { name: "Key_F5"; type: "number"; isReadonly: true }
        Property { name: "Key_F6"; type: "number"; isReadonly: true }
        Property { name: "Key_F7"; type: "number"; isReadonly: true }
        Property { name: "Key_F8"; type: "number"; isReadonly: true }
        Property { name: "Key_F9"; type: "number"; isReadonly: true }
        Property { name: "Key_F10"; type: "number"; isReadonly: true }
        Property { name: "Key_F11"; type: "number"; isReadonly: true }
        Property { name: "Key_F12"; type: "number"; isReadonly: true }
        Property { name: "Key_F13"; type: "number"; isReadonly: true }
        Property { name: "Key_F14"; type: "number"; isReadonly: true }
        Property { name: "Key_F15"; type: "number"; isReadonly: true }
        Property { name: "Key_F16"; type: "number"; isReadonly: true }
        Property { name: "Key_F17"; type: "number"; isReadonly: true }
        Property { name: "Key_F18"; type: "number"; isReadonly: true }
        Property { name: "Key_F19"; type: "number"; isReadonly: true }
        Property { name: "Key_F20"; type: "number"; isReadonly: true }
        Property { name: "Key_F21"; type: "number"; isReadonly: true }
        Property { name: "Key_F22"; type: "number"; isReadonly: true }
        Property { name: "Key_F23"; type: "number"; isReadonly: true }
        Property { name: "Key_F24"; type: "number"; isReadonly: true }
        Property { name: "Key_F25"; type: "number"; isReadonly: true }
        Property { name: "Key_F26"; type: "number"; isReadonly: true }
        Property { name: "Key_F27"; type: "number"; isReadonly: true }
        Property { name: "Key_F28"; type: "number"; isReadonly: true }
        Property { name: "Key_F29"; type: "number"; isReadonly: true }
        Property { name: "Key_F30"; type: "number"; isReadonly: true }
        Property { name: "Key_F31"; type: "number"; isReadonly: true }
        Property { name: "Key_F32"; type: "number"; isReadonly: true }
        Property { name: "Key_F33"; type: "number"; isReadonly: true }
        Property { name: "Key_F34"; type: "number"; isReadonly: true }
        Property { name: "Key_F35"; type: "number"; isReadonly: true }
        Property { name: "Key_Super_L"; type: "number"; isReadonly: true }
        Property { name: "Key_Super_R"; type: "number"; isReadonly: true }
        Property { name: "Key_Menu"; type: "number"; isReadonly: true }
        Property { name: "Key_Hyper_L"; type: "number"; isReadonly: true }
        Property { name: "Key_Hyper_R"; type: "number"; isReadonly: true }
        Property { name: "Key_Help"; type: "number"; isReadonly: true }
        Property { name: "Key_Direction_L"; type: "number"; isReadonly: true }
        Property { name: "Key_Direction_R"; type: "number"; isReadonly: true }
        Property { name: "Key_AltGr"; type: "number"; isReadonly: true }
        Property { name: "Key_Multi_key"; type: "number"; isReadonly: true }
        Property { name: "Key_Codeinput"; type: "number"; isReadonly: true }
        Property { name: "Key_SingleCandidate"; type: "number"; isReadonly: true }
        Property { name: "Key_MultipleCandidate"; type: "number"; isReadonly: true }
        Property { name: "Key_PreviousCandidate"; type: "number"; isReadonly: true }
        Property { name: "Key_Mode_switch"; type: "number"; isReadonly: true }
        Property { name: "Key_Kanji"; type: "number"; isReadonly: true }
        Property { name: "Key_Muhenkan"; type: "number"; isReadonly: true }
        Property { name: "Key_Henkan"; type: "number"; isReadonly: true }
        Property { name: "Key_Romaji"; type: "number"; isReadonly: true }
        Property { name: "Key_Hiragana"; type: "number"; isReadonly: true }
        Property { name: "Key_Katakana"; type: "number"; isReadonly: true }
        Property { name: "Key_Hiragana_Katakana"; type: "number"; isReadonly: true }
        Property { name: "Key_Zenkaku"; type: "number"; isReadonly: true }
        Property { name: "Key_Hankaku"; type: "number"; isReadonly: true }
        Property { name: "Key_Zenkaku_Hankaku"; type: "number"; isReadonly: true }
        Property { name: "Key_Touroku"; type: "number"; isReadonly: true }
        Property { name: "Key_Massyo"; type: "number"; isReadonly: true }
        Property { name: "Key_Kana_Lock"; type: "number"; isReadonly: true }
        Property { name: "Key_Kana_Shift"; type: "number"; isReadonly: true }
        Property { name: "Key_Eisu_Shift"; type: "number"; isReadonly: true }
        Property { name: "Key_Eisu_toggle"; type: "number"; isReadonly: true }
        Property { name: "Key_Hangul"; type: "number"; isReadonly: true }
        Property { name: "Key_Hangul_Start"; type: "number"; isReadonly: true }
        Property { name: "Key_Hangul_End"; type: "number"; isReadonly: true }
        Property { name: "Key_Hangul_Hanja"; type: "number"; isReadonly: true }
        Property { name: "Key_Hangul_Jamo"; type: "number"; isReadonly: true }
        Property { name: "Key_Hangul_Romaja"; type: "number"; isReadonly: true }
        Property { name: "Key_Hangul_Jeonja"; type: "number"; isReadonly: true }
        Property { name: "Key_Hangul_Banja"; type: "number"; isReadonly: true }
        Property { name: "Key_Hangul_PreHanja"; type: "number"; isReadonly: true }
        Property { name: "Key_Hangul_PostHanja"; type: "number"; isReadonly: true }
        Property { name: "Key_Hangul_Special"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Grave"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Acute"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Circumflex"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Tilde"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Macron"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Breve"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Abovedot"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Diaeresis"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Abovering"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Doubleacute"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Caron"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Cedilla"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Ogonek"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Iota"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Voiced_Sound"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Semivoiced_Sound"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Belowdot"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Hook"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Horn"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Stroke"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Abovecomma"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Abovereversedcomma"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Doublegrave"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Belowring"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Belowmacron"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Belowcircumflex"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Belowtilde"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Belowbreve"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Belowdiaeresis"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Invertedbreve"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Belowcomma"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Currency"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_a"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_A"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_e"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_E"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_i"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_I"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_o"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_O"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_u"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_U"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Small_Schwa"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Capital_Schwa"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Greek"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Lowline"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Aboveverticalline"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Belowverticalline"; type: "number"; isReadonly: true }
        Property { name: "Key_Dead_Longsolidusoverlay"; type: "number"; isReadonly: true }
        Property { name: "Key_Back"; type: "number"; isReadonly: true }
        Property { name: "Key_Forward"; type: "number"; isReadonly: true }
        Property { name: "Key_Stop"; type: "number"; isReadonly: true }
        Property { name: "Key_Refresh"; type: "number"; isReadonly: true }
        Property { name: "Key_VolumeDown"; type: "number"; isReadonly: true }
        Property { name: "Key_VolumeMute"; type: "number"; isReadonly: true }
        Property { name: "Key_VolumeUp"; type: "number"; isReadonly: true }
        Property { name: "Key_BassBoost"; type: "number"; isReadonly: true }
        Property { name: "Key_BassUp"; type: "number"; isReadonly: true }
        Property { name: "Key_BassDown"; type: "number"; isReadonly: true }
        Property { name: "Key_TrebleUp"; type: "number"; isReadonly: true }
        Property { name: "Key_TrebleDown"; type: "number"; isReadonly: true }
        Property { name: "Key_MediaPlay"; type: "number"; isReadonly: true }
        Property { name: "Key_MediaStop"; type: "number"; isReadonly: true }
        Property { name: "Key_MediaPrevious"; type: "number"; isReadonly: true }
        Property { name: "Key_MediaNext"; type: "number"; isReadonly: true }
        Property { name: "Key_MediaRecord"; type: "number"; isReadonly: true }
        Property { name: "Key_MediaPause"; type: "number"; isReadonly: true }
        Property { name: "Key_MediaTogglePlayPause"; type: "number"; isReadonly: true }
        Property { name: "Key_HomePage"; type: "number"; isReadonly: true }
        Property { name: "Key_Favorites"; type: "number"; isReadonly: true }
        Property { name: "Key_Search"; type: "number"; isReadonly: true }
        Property { name: "Key_Standby"; type: "number"; isReadonly: true }
        Property { name: "Key_OpenUrl"; type: "number"; isReadonly: true }
        Property { name: "Key_LaunchMail"; type: "number"; isReadonly: true }
        Property { name: "Key_LaunchMedia"; type: "number"; isReadonly: true }
        Property { name: "Key_Launch0"; type: "number"; isReadonly: true }
        Property { name: "Key_Launch1"; type: "number"; isReadonly: true }
        Property { name: "Key_Launch2"; type: "number"; isReadonly: true }
        Property { name: "Key_Launch3"; type: "number"; isReadonly: true }
        Property { name: "Key_Launch4"; type: "number"; isReadonly: true }
        Property { name: "Key_Launch5"; type: "number"; isReadonly: true }
        Property { name: "Key_Launch6"; type: "number"; isReadonly: true }
        Property { name: "Key_Launch7"; type: "number"; isReadonly: true }
        Property { name: "Key_Launch8"; type: "number"; isReadonly: true }
        Property { name: "Key_Launch9"; type: "number"; isReadonly: true }
        Property { name: "Key_LaunchA"; type: "number"; isReadonly: true }
        Property { name: "Key_LaunchB"; type: "number"; isReadonly: true }
        Property { name: "Key_LaunchC"; type: "number"; isReadonly: true }
        Property { name: "Key_LaunchD"; type: "number"; isReadonly: true }
        Property { name: "Key_LaunchE"; type: "number"; isReadonly: true }
        Property { name: "Key_LaunchF"; type: "number"; isReadonly: true }
        Property { name: "Key_MonBrightnessUp"; type: "number"; isReadonly: true }
        Property { name: "Key_MonBrightnessDown"; type: "number"; isReadonly: true }
        Property { name: "Key_KeyboardLightOnOff"; type: "number"; isReadonly: true }
        Property { name: "Key_KeyboardBrightnessUp"; type: "number"; isReadonly: true }
        Property { name: "Key_KeyboardBrightnessDown"; type: "number"; isReadonly: true }
        Property { name: "Key_PowerOff"; type: "number"; isReadonly: true }
        Property { name: "Key_WakeUp"; type: "number"; isReadonly: true }
        Property { name: "Key_Eject"; type: "number"; isReadonly: true }
        Property { name: "Key_ScreenSaver"; type: "number"; isReadonly: true }
        Property { name: "Key_WWW"; type: "number"; isReadonly: true }
        Property { name: "Key_Memo"; type: "number"; isReadonly: true }
        Property { name: "Key_LightBulb"; type: "number"; isReadonly: true }
        Property { name: "Key_Shop"; type: "number"; isReadonly: true }
        Property { name: "Key_History"; type: "number"; isReadonly: true }
        Property { name: "Key_AddFavorite"; type: "number"; isReadonly: true }
        Property { name: "Key_HotLinks"; type: "number"; isReadonly: true }
        Property { name: "Key_BrightnessAdjust"; type: "number"; isReadonly: true }
        Property { name: "Key_Finance"; type: "number"; isReadonly: true }
        Property { name: "Key_Community"; type: "number"; isReadonly: true }
        Property { name: "Key_AudioRewind"; type: "number"; isReadonly: true }
        Property { name: "Key_BackForward"; type: "number"; isReadonly: true }
        Property { name: "Key_ApplicationLeft"; type: "number"; isReadonly: true }
        Property { name: "Key_ApplicationRight"; type: "number"; isReadonly: true }
        Property { name: "Key_Book"; type: "number"; isReadonly: true }
        Property { name: "Key_CD"; type: "number"; isReadonly: true }
        Property { name: "Key_Calculator"; type: "number"; isReadonly: true }
        Property { name: "Key_ToDoList"; type: "number"; isReadonly: true }
        Property { name: "Key_ClearGrab"; type: "number"; isReadonly: true }
        Property { name: "Key_Close"; type: "number"; isReadonly: true }
        Property { name: "Key_Copy"; type: "number"; isReadonly: true }
        Property { name: "Key_Cut"; type: "number"; isReadonly: true }
        Property { name: "Key_Display"; type: "number"; isReadonly: true }
        Property { name: "Key_DOS"; type: "number"; isReadonly: true }
        Property { name: "Key_Documents"; type: "number"; isReadonly: true }
        Property { name: "Key_Excel"; type: "number"; isReadonly: true }
        Property { name: "Key_Explorer"; type: "number"; isReadonly: true }
        Property { name: "Key_Game"; type: "number"; isReadonly: true }
        Property { name: "Key_Go"; type: "number"; isReadonly: true }
        Property { name: "Key_iTouch"; type: "number"; isReadonly: true }
        Property { name: "Key_LogOff"; type: "number"; isReadonly: true }
        Property { name: "Key_Market"; type: "number"; isReadonly: true }
        Property { name: "Key_Meeting"; type: "number"; isReadonly: true }
        Property { name: "Key_MenuKB"; type: "number"; isReadonly: true }
        Property { name: "Key_MenuPB"; type: "number"; isReadonly: true }
        Property { name: "Key_MySites"; type: "number"; isReadonly: true }
        Property { name: "Key_News"; type: "number"; isReadonly: true }
        Property { name: "Key_OfficeHome"; type: "number"; isReadonly: true }
        Property { name: "Key_Option"; type: "number"; isReadonly: true }
        Property { name: "Key_Paste"; type: "number"; isReadonly: true }
        Property { name: "Key_Phone"; type: "number"; isReadonly: true }
        Property { name: "Key_Calendar"; type: "number"; isReadonly: true }
        Property { name: "Key_Reply"; type: "number"; isReadonly: true }
        Property { name: "Key_Reload"; type: "number"; isReadonly: true }
        Property { name: "Key_RotateWindows"; type: "number"; isReadonly: true }
        Property { name: "Key_RotationPB"; type: "number"; isReadonly: true }
        Property { name: "Key_RotationKB"; type: "number"; isReadonly: true }
        Property { name: "Key_Save"; type: "number"; isReadonly: true }
        Property { name: "Key_Send"; type: "number"; isReadonly: true }
        Property { name: "Key_Spell"; type: "number"; isReadonly: true }
        Property { name: "Key_SplitScreen"; type: "number"; isReadonly: true }
        Property { name: "Key_Support"; type: "number"; isReadonly: true }
        Property { name: "Key_TaskPane"; type: "number"; isReadonly: true }
        Property { name: "Key_Terminal"; type: "number"; isReadonly: true }
        Property { name: "Key_Tools"; type: "number"; isReadonly: true }
        Property { name: "Key_Travel"; type: "number"; isReadonly: true }
        Property { name: "Key_Video"; type: "number"; isReadonly: true }
        Property { name: "Key_Word"; type: "number"; isReadonly: true }
        Property { name: "Key_Xfer"; type: "number"; isReadonly: true }
        Property { name: "Key_ZoomIn"; type: "number"; isReadonly: true }
        Property { name: "Key_ZoomOut"; type: "number"; isReadonly: true }
        Property { name: "Key_Away"; type: "number"; isReadonly: true }
        Property { name: "Key_Messenger"; type: "number"; isReadonly: true }
        Property { name: "Key_WebCam"; type: "number"; isReadonly: true }
        Property { name: "Key_MailForward"; type: "number"; isReadonly: true }
        Property { name: "Key_Pictures"; type: "number"; isReadonly: true }
        Property { name: "Key_Music"; type: "number"; isReadonly: true }
        Property { name: "Key_Battery"; type: "number"; isReadonly: true }
        Property { name: "Key_Bluetooth"; type: "number"; isReadonly: true }
        Property { name: "Key_WLAN"; type: "number"; isReadonly: true }
        Property { name: "Key_UWB"; type: "number"; isReadonly: true }
        Property { name: "Key_AudioForward"; type: "number"; isReadonly: true }
        Property { name: "Key_AudioRepeat"; type: "number"; isReadonly: true }
        Property { name: "Key_AudioRandomPlay"; type: "number"; isReadonly: true }
        Property { name: "Key_Subtitle"; type: "number"; isReadonly: true }
        Property { name: "Key_AudioCycleTrack"; type: "number"; isReadonly: true }
        Property { name: "Key_Time"; type: "number"; isReadonly: true }
        Property { name: "Key_Hibernate"; type: "number"; isReadonly: true }
        Property { name: "Key_View"; type: "number"; isReadonly: true }
        Property { name: "Key_TopMenu"; type: "number"; isReadonly: true }
        Property { name: "Key_PowerDown"; type: "number"; isReadonly: true }
        Property { name: "Key_Suspend"; type: "number"; isReadonly: true }
        Property { name: "Key_ContrastAdjust"; type: "number"; isReadonly: true }
        Property { name: "Key_LaunchG"; type: "number"; isReadonly: true }
        Property { name: "Key_LaunchH"; type: "number"; isReadonly: true }
        Property { name: "Key_TouchpadToggle"; type: "number"; isReadonly: true }
        Property { name: "Key_TouchpadOn"; type: "number"; isReadonly: true }
        Property { name: "Key_TouchpadOff"; type: "number"; isReadonly: true }
        Property { name: "Key_MicMute"; type: "number"; isReadonly: true }
        Property { name: "Key_Red"; type: "number"; isReadonly: true }
        Property { name: "Key_Green"; type: "number"; isReadonly: true }
        Property { name: "Key_Yellow"; type: "number"; isReadonly: true }
        Property { name: "Key_Blue"; type: "number"; isReadonly: true }
        Property { name: "Key_ChannelUp"; type: "number"; isReadonly: true }
        Property { name: "Key_ChannelDown"; type: "number"; isReadonly: true }
        Property { name: "Key_Guide"; type: "number"; isReadonly: true }
        Property { name: "Key_Info"; type: "number"; isReadonly: true }
        Property { name: "Key_Settings"; type: "number"; isReadonly: true }
        Property { name: "Key_MicVolumeUp"; type: "number"; isReadonly: true }
        Property { name: "Key_MicVolumeDown"; type: "number"; isReadonly: true }
        Property { name: "Key_New"; type: "number"; isReadonly: true }
        Property { name: "Key_Open"; type: "number"; isReadonly: true }
        Property { name: "Key_Find"; type: "number"; isReadonly: true }
        Property { name: "Key_Undo"; type: "number"; isReadonly: true }
        Property { name: "Key_Redo"; type: "number"; isReadonly: true }
        Property { name: "Key_MediaLast"; type: "number"; isReadonly: true }
        Property { name: "Key_Select"; type: "number"; isReadonly: true }
        Property { name: "Key_Yes"; type: "number"; isReadonly: true }
        Property { name: "Key_No"; type: "number"; isReadonly: true }
        Property { name: "Key_Cancel"; type: "number"; isReadonly: true }
        Property { name: "Key_Printer"; type: "number"; isReadonly: true }
        Property { name: "Key_Execute"; type: "number"; isReadonly: true }
        Property { name: "Key_Sleep"; type: "number"; isReadonly: true }
        Property { name: "Key_Play"; type: "number"; isReadonly: true }
        Property { name: "Key_Zoom"; type: "number"; isReadonly: true }
        Property { name: "Key_Exit"; type: "number"; isReadonly: true }
        Property { name: "Key_Context1"; type: "number"; isReadonly: true }
        Property { name: "Key_Context2"; type: "number"; isReadonly: true }
        Property { name: "Key_Context3"; type: "number"; isReadonly: true }
        Property { name: "Key_Context4"; type: "number"; isReadonly: true }
        Property { name: "Key_Call"; type: "number"; isReadonly: true }
        Property { name: "Key_Hangup"; type: "number"; isReadonly: true }
        Property { name: "Key_Flip"; type: "number"; isReadonly: true }
        Property { name: "Key_ToggleCallHangup"; type: "number"; isReadonly: true }
        Property { name: "Key_VoiceDial"; type: "number"; isReadonly: true }
        Property { name: "Key_LastNumberRedial"; type: "number"; isReadonly: true }
        Property { name: "Key_Camera"; type: "number"; isReadonly: true }
        Property { name: "Key_CameraFocus"; type: "number"; isReadonly: true }
        Property { name: "Key_unknown"; type: "number"; isReadonly: true }
        Property { name: "NoModifier"; type: "number"; isReadonly: true }
        Property { name: "ShiftModifier"; type: "number"; isReadonly: true }
        Property { name: "ControlModifier"; type: "number"; isReadonly: true }
        Property { name: "AltModifier"; type: "number"; isReadonly: true }
        Property { name: "MetaModifier"; type: "number"; isReadonly: true }
        Property { name: "KeypadModifier"; type: "number"; isReadonly: true }
        Property { name: "GroupSwitchModifier"; type: "number"; isReadonly: true }
        Property { name: "KeyboardModifierMask"; type: "number"; isReadonly: true }
        Property { name: "META"; type: "number"; isReadonly: true }
        Property { name: "SHIFT"; type: "number"; isReadonly: true }
        Property { name: "CTRL"; type: "number"; isReadonly: true }
        Property { name: "ALT"; type: "number"; isReadonly: true }
        Property { name: "MODIFIER_MASK"; type: "number"; isReadonly: true }
        Property { name: "NoArrow"; type: "number"; isReadonly: true }
        Property { name: "UpArrow"; type: "number"; isReadonly: true }
        Property { name: "DownArrow"; type: "number"; isReadonly: true }
        Property { name: "LeftArrow"; type: "number"; isReadonly: true }
        Property { name: "RightArrow"; type: "number"; isReadonly: true }
        Property { name: "NoPen"; type: "number"; isReadonly: true }
        Property { name: "SolidLine"; type: "number"; isReadonly: true }
        Property { name: "DashLine"; type: "number"; isReadonly: true }
        Property { name: "DotLine"; type: "number"; isReadonly: true }
        Property { name: "DashDotLine"; type: "number"; isReadonly: true }
        Property { name: "DashDotDotLine"; type: "number"; isReadonly: true }
        Property { name: "CustomDashLine"; type: "number"; isReadonly: true }
        Property { name: "FlatCap"; type: "number"; isReadonly: true }
        Property { name: "SquareCap"; type: "number"; isReadonly: true }
        Property { name: "RoundCap"; type: "number"; isReadonly: true }
        Property { name: "MPenCapStyle"; type: "number"; isReadonly: true }
        Property { name: "MiterJoin"; type: "number"; isReadonly: true }
        Property { name: "BevelJoin"; type: "number"; isReadonly: true }
        Property { name: "RoundJoin"; type: "number"; isReadonly: true }
        Property { name: "SvgMiterJoin"; type: "number"; isReadonly: true }
        Property { name: "MPenJoinStyle"; type: "number"; isReadonly: true }
        Property { name: "NoBrush"; type: "number"; isReadonly: true }
        Property { name: "SolidPattern"; type: "number"; isReadonly: true }
        Property { name: "Dense1Pattern"; type: "number"; isReadonly: true }
        Property { name: "Dense2Pattern"; type: "number"; isReadonly: true }
        Property { name: "Dense3Pattern"; type: "number"; isReadonly: true }
        Property { name: "Dense4Pattern"; type: "number"; isReadonly: true }
        Property { name: "Dense5Pattern"; type: "number"; isReadonly: true }
        Property { name: "Dense6Pattern"; type: "number"; isReadonly: true }
        Property { name: "Dense7Pattern"; type: "number"; isReadonly: true }
        Property { name: "HorPattern"; type: "number"; isReadonly: true }
        Property { name: "VerPattern"; type: "number"; isReadonly: true }
        Property { name: "CrossPattern"; type: "number"; isReadonly: true }
        Property { name: "BDiagPattern"; type: "number"; isReadonly: true }
        Property { name: "FDiagPattern"; type: "number"; isReadonly: true }
        Property { name: "DiagCrossPattern"; type: "number"; isReadonly: true }
        Property { name: "LinearGradientPattern"; type: "number"; isReadonly: true }
        Property { name: "RadialGradientPattern"; type: "number"; isReadonly: true }
        Property { name: "ConicalGradientPattern"; type: "number"; isReadonly: true }
        Property { name: "TexturePattern"; type: "number"; isReadonly: true }
        Property { name: "AbsoluteSize"; type: "number"; isReadonly: true }
        Property { name: "RelativeSize"; type: "number"; isReadonly: true }
        Property { name: "ArrowCursor"; type: "number"; isReadonly: true }
        Property { name: "UpArrowCursor"; type: "number"; isReadonly: true }
        Property { name: "CrossCursor"; type: "number"; isReadonly: true }
        Property { name: "WaitCursor"; type: "number"; isReadonly: true }
        Property { name: "IBeamCursor"; type: "number"; isReadonly: true }
        Property { name: "SizeVerCursor"; type: "number"; isReadonly: true }
        Property { name: "SizeHorCursor"; type: "number"; isReadonly: true }
        Property { name: "SizeBDiagCursor"; type: "number"; isReadonly: true }
        Property { name: "SizeFDiagCursor"; type: "number"; isReadonly: true }
        Property { name: "SizeAllCursor"; type: "number"; isReadonly: true }
        Property { name: "BlankCursor"; type: "number"; isReadonly: true }
        Property { name: "SplitVCursor"; type: "number"; isReadonly: true }
        Property { name: "SplitHCursor"; type: "number"; isReadonly: true }
        Property { name: "PointingHandCursor"; type: "number"; isReadonly: true }
        Property { name: "ForbiddenCursor"; type: "number"; isReadonly: true }
        Property { name: "WhatsThisCursor"; type: "number"; isReadonly: true }
        Property { name: "BusyCursor"; type: "number"; isReadonly: true }
        Property { name: "OpenHandCursor"; type: "number"; isReadonly: true }
        Property { name: "ClosedHandCursor"; type: "number"; isReadonly: true }
        Property { name: "DragCopyCursor"; type: "number"; isReadonly: true }
        Property { name: "DragMoveCursor"; type: "number"; isReadonly: true }
        Property { name: "DragLinkCursor"; type: "number"; isReadonly: true }
        Property { name: "LastCursor"; type: "number"; isReadonly: true }
        Property { name: "BitmapCursor"; type: "number"; isReadonly: true }
        Property { name: "CustomCursor"; type: "number"; isReadonly: true }
        Property { name: "PlainText"; type: "number"; isReadonly: true }
        Property { name: "RichText"; type: "number"; isReadonly: true }
        Property { name: "AutoText"; type: "number"; isReadonly: true }
        Property { name: "MarkdownText"; type: "number"; isReadonly: true }
        Property { name: "IgnoreAspectRatio"; type: "number"; isReadonly: true }
        Property { name: "KeepAspectRatio"; type: "number"; isReadonly: true }
        Property { name: "KeepAspectRatioByExpanding"; type: "number"; isReadonly: true }
        Property { name: "LeftDockWidgetArea"; type: "number"; isReadonly: true }
        Property { name: "RightDockWidgetArea"; type: "number"; isReadonly: true }
        Property { name: "TopDockWidgetArea"; type: "number"; isReadonly: true }
        Property { name: "BottomDockWidgetArea"; type: "number"; isReadonly: true }
        Property { name: "DockWidgetArea_Mask"; type: "number"; isReadonly: true }
        Property { name: "AllDockWidgetAreas"; type: "number"; isReadonly: true }
        Property { name: "NoDockWidgetArea"; type: "number"; isReadonly: true }
        Property { name: "LeftToolBarArea"; type: "number"; isReadonly: true }
        Property { name: "RightToolBarArea"; type: "number"; isReadonly: true }
        Property { name: "TopToolBarArea"; type: "number"; isReadonly: true }
        Property { name: "BottomToolBarArea"; type: "number"; isReadonly: true }
        Property { name: "ToolBarArea_Mask"; type: "number"; isReadonly: true }
        Property { name: "AllToolBarAreas"; type: "number"; isReadonly: true }
        Property { name: "NoToolBarArea"; type: "number"; isReadonly: true }
        Property { name: "TextDate"; type: "number"; isReadonly: true }
        Property { name: "ISODate"; type: "number"; isReadonly: true }
        Property { name: "RFC2822Date"; type: "number"; isReadonly: true }
        Property { name: "ISODateWithMs"; type: "number"; isReadonly: true }
        Property { name: "LocalTime"; type: "number"; isReadonly: true }
        Property { name: "UTC"; type: "number"; isReadonly: true }
        Property { name: "OffsetFromUTC"; type: "number"; isReadonly: true }
        Property { name: "TimeZone"; type: "number"; isReadonly: true }
        Property { name: "Monday"; type: "number"; isReadonly: true }
        Property { name: "Tuesday"; type: "number"; isReadonly: true }
        Property { name: "Wednesday"; type: "number"; isReadonly: true }
        Property { name: "Thursday"; type: "number"; isReadonly: true }
        Property { name: "Friday"; type: "number"; isReadonly: true }
        Property { name: "Saturday"; type: "number"; isReadonly: true }
        Property { name: "Sunday"; type: "number"; isReadonly: true }
        Property { name: "ScrollBarAsNeeded"; type: "number"; isReadonly: true }
        Property { name: "ScrollBarAlwaysOff"; type: "number"; isReadonly: true }
        Property { name: "ScrollBarAlwaysOn"; type: "number"; isReadonly: true }
        Property { name: "CaseInsensitive"; type: "number"; isReadonly: true }
        Property { name: "CaseSensitive"; type: "number"; isReadonly: true }
        Property { name: "TopLeftCorner"; type: "number"; isReadonly: true }
        Property { name: "TopRightCorner"; type: "number"; isReadonly: true }
        Property { name: "BottomLeftCorner"; type: "number"; isReadonly: true }
        Property { name: "BottomRightCorner"; type: "number"; isReadonly: true }
        Property { name: "TopEdge"; type: "number"; isReadonly: true }
        Property { name: "LeftEdge"; type: "number"; isReadonly: true }
        Property { name: "RightEdge"; type: "number"; isReadonly: true }
        Property { name: "BottomEdge"; type: "number"; isReadonly: true }
        Property { name: "AutoConnection"; type: "number"; isReadonly: true }
        Property { name: "DirectConnection"; type: "number"; isReadonly: true }
        Property { name: "QueuedConnection"; type: "number"; isReadonly: true }
        Property { name: "BlockingQueuedConnection"; type: "number"; isReadonly: true }
        Property { name: "UniqueConnection"; type: "number"; isReadonly: true }
        Property { name: "SingleShotConnection"; type: "number"; isReadonly: true }
        Property { name: "WidgetShortcut"; type: "number"; isReadonly: true }
        Property { name: "WindowShortcut"; type: "number"; isReadonly: true }
        Property { name: "ApplicationShortcut"; type: "number"; isReadonly: true }
        Property { name: "WidgetWithChildrenShortcut"; type: "number"; isReadonly: true }
        Property { name: "OddEvenFill"; type: "number"; isReadonly: true }
        Property { name: "WindingFill"; type: "number"; isReadonly: true }
        Property { name: "MaskInColor"; type: "number"; isReadonly: true }
        Property { name: "MaskOutColor"; type: "number"; isReadonly: true }
        Property { name: "NoClip"; type: "number"; isReadonly: true }
        Property { name: "ReplaceClip"; type: "number"; isReadonly: true }
        Property { name: "IntersectClip"; type: "number"; isReadonly: true }
        Property { name: "ContainsItemShape"; type: "number"; isReadonly: true }
        Property { name: "IntersectsItemShape"; type: "number"; isReadonly: true }
        Property { name: "ContainsItemBoundingRect"; type: "number"; isReadonly: true }
        Property { name: "IntersectsItemBoundingRect"; type: "number"; isReadonly: true }
        Property { name: "ReplaceSelection"; type: "number"; isReadonly: true }
        Property { name: "AddToSelection"; type: "number"; isReadonly: true }
        Property { name: "FastTransformation"; type: "number"; isReadonly: true }
        Property { name: "SmoothTransformation"; type: "number"; isReadonly: true }
        Property { name: "XAxis"; type: "number"; isReadonly: true }
        Property { name: "YAxis"; type: "number"; isReadonly: true }
        Property { name: "ZAxis"; type: "number"; isReadonly: true }
        Property { name: "MouseFocusReason"; type: "number"; isReadonly: true }
        Property { name: "TabFocusReason"; type: "number"; isReadonly: true }
        Property { name: "BacktabFocusReason"; type: "number"; isReadonly: true }
        Property { name: "ActiveWindowFocusReason"; type: "number"; isReadonly: true }
        Property { name: "PopupFocusReason"; type: "number"; isReadonly: true }
        Property { name: "ShortcutFocusReason"; type: "number"; isReadonly: true }
        Property { name: "MenuBarFocusReason"; type: "number"; isReadonly: true }
        Property { name: "OtherFocusReason"; type: "number"; isReadonly: true }
        Property { name: "NoFocusReason"; type: "number"; isReadonly: true }
        Property { name: "NoContextMenu"; type: "number"; isReadonly: true }
        Property { name: "DefaultContextMenu"; type: "number"; isReadonly: true }
        Property { name: "ActionsContextMenu"; type: "number"; isReadonly: true }
        Property { name: "CustomContextMenu"; type: "number"; isReadonly: true }
        Property { name: "PreventContextMenu"; type: "number"; isReadonly: true }
        Property { name: "Press"; type: "number"; isReadonly: true }
        Property { name: "Release"; type: "number"; isReadonly: true }
        Property { name: "ImEnabled"; type: "number"; isReadonly: true }
        Property { name: "ImCursorRectangle"; type: "number"; isReadonly: true }
        Property { name: "ImFont"; type: "number"; isReadonly: true }
        Property { name: "ImCursorPosition"; type: "number"; isReadonly: true }
        Property { name: "ImSurroundingText"; type: "number"; isReadonly: true }
        Property { name: "ImCurrentSelection"; type: "number"; isReadonly: true }
        Property { name: "ImMaximumTextLength"; type: "number"; isReadonly: true }
        Property { name: "ImAnchorPosition"; type: "number"; isReadonly: true }
        Property { name: "ImHints"; type: "number"; isReadonly: true }
        Property { name: "ImPreferredLanguage"; type: "number"; isReadonly: true }
        Property { name: "ImAbsolutePosition"; type: "number"; isReadonly: true }
        Property { name: "ImTextBeforeCursor"; type: "number"; isReadonly: true }
        Property { name: "ImTextAfterCursor"; type: "number"; isReadonly: true }
        Property { name: "ImEnterKeyType"; type: "number"; isReadonly: true }
        Property { name: "ImAnchorRectangle"; type: "number"; isReadonly: true }
        Property { name: "ImInputItemClipRectangle"; type: "number"; isReadonly: true }
        Property { name: "ImReadOnly"; type: "number"; isReadonly: true }
        Property { name: "ImPlatformData"; type: "number"; isReadonly: true }
        Property { name: "ImQueryInput"; type: "number"; isReadonly: true }
        Property { name: "ImQueryAll"; type: "number"; isReadonly: true }
        Property { name: "ImhNone"; type: "number"; isReadonly: true }
        Property { name: "ImhHiddenText"; type: "number"; isReadonly: true }
        Property { name: "ImhSensitiveData"; type: "number"; isReadonly: true }
        Property { name: "ImhNoAutoUppercase"; type: "number"; isReadonly: true }
        Property { name: "ImhPreferNumbers"; type: "number"; isReadonly: true }
        Property { name: "ImhPreferUppercase"; type: "number"; isReadonly: true }
        Property { name: "ImhPreferLowercase"; type: "number"; isReadonly: true }
        Property { name: "ImhNoPredictiveText"; type: "number"; isReadonly: true }
        Property { name: "ImhDate"; type: "number"; isReadonly: true }
        Property { name: "ImhTime"; type: "number"; isReadonly: true }
        Property { name: "ImhPreferLatin"; type: "number"; isReadonly: true }
        Property { name: "ImhMultiLine"; type: "number"; isReadonly: true }
        Property { name: "ImhNoEditMenu"; type: "number"; isReadonly: true }
        Property { name: "ImhNoTextHandles"; type: "number"; isReadonly: true }
        Property { name: "ImhDigitsOnly"; type: "number"; isReadonly: true }
        Property { name: "ImhFormattedNumbersOnly"; type: "number"; isReadonly: true }
        Property { name: "ImhUppercaseOnly"; type: "number"; isReadonly: true }
        Property { name: "ImhLowercaseOnly"; type: "number"; isReadonly: true }
        Property { name: "ImhDialableCharactersOnly"; type: "number"; isReadonly: true }
        Property { name: "ImhEmailCharactersOnly"; type: "number"; isReadonly: true }
        Property { name: "ImhUrlCharactersOnly"; type: "number"; isReadonly: true }
        Property { name: "ImhLatinOnly"; type: "number"; isReadonly: true }
        Property { name: "ImhExclusiveInputMask"; type: "number"; isReadonly: true }
        Property { name: "EnterKeyDefault"; type: "number"; isReadonly: true }
        Property { name: "EnterKeyReturn"; type: "number"; isReadonly: true }
        Property { name: "EnterKeyDone"; type: "number"; isReadonly: true }
        Property { name: "EnterKeyGo"; type: "number"; isReadonly: true }
        Property { name: "EnterKeySend"; type: "number"; isReadonly: true }
        Property { name: "EnterKeySearch"; type: "number"; isReadonly: true }
        Property { name: "EnterKeyNext"; type: "number"; isReadonly: true }
        Property { name: "EnterKeyPrevious"; type: "number"; isReadonly: true }
        Property { name: "ToolButtonIconOnly"; type: "number"; isReadonly: true }
        Property { name: "ToolButtonTextOnly"; type: "number"; isReadonly: true }
        Property { name: "ToolButtonTextBesideIcon"; type: "number"; isReadonly: true }
        Property { name: "ToolButtonTextUnderIcon"; type: "number"; isReadonly: true }
        Property { name: "ToolButtonFollowStyle"; type: "number"; isReadonly: true }
        Property { name: "LeftToRight"; type: "number"; isReadonly: true }
        Property { name: "RightToLeft"; type: "number"; isReadonly: true }
        Property { name: "LayoutDirectionAuto"; type: "number"; isReadonly: true }
        Property { name: "CopyAction"; type: "number"; isReadonly: true }
        Property { name: "MoveAction"; type: "number"; isReadonly: true }
        Property { name: "LinkAction"; type: "number"; isReadonly: true }
        Property { name: "ActionMask"; type: "number"; isReadonly: true }
        Property { name: "TargetMoveAction"; type: "number"; isReadonly: true }
        Property { name: "IgnoreAction"; type: "number"; isReadonly: true }
        Property { name: "Unchecked"; type: "number"; isReadonly: true }
        Property { name: "PartiallyChecked"; type: "number"; isReadonly: true }
        Property { name: "Checked"; type: "number"; isReadonly: true }
        Property { name: "DisplayRole"; type: "number"; isReadonly: true }
        Property { name: "DecorationRole"; type: "number"; isReadonly: true }
        Property { name: "EditRole"; type: "number"; isReadonly: true }
        Property { name: "ToolTipRole"; type: "number"; isReadonly: true }
        Property { name: "StatusTipRole"; type: "number"; isReadonly: true }
        Property { name: "WhatsThisRole"; type: "number"; isReadonly: true }
        Property { name: "FontRole"; type: "number"; isReadonly: true }
        Property { name: "TextAlignmentRole"; type: "number"; isReadonly: true }
        Property { name: "BackgroundRole"; type: "number"; isReadonly: true }
        Property { name: "ForegroundRole"; type: "number"; isReadonly: true }
        Property { name: "CheckStateRole"; type: "number"; isReadonly: true }
        Property { name: "AccessibleTextRole"; type: "number"; isReadonly: true }
        Property { name: "AccessibleDescriptionRole"; type: "number"; isReadonly: true }
        Property { name: "SizeHintRole"; type: "number"; isReadonly: true }
        Property { name: "InitialSortOrderRole"; type: "number"; isReadonly: true }
        Property { name: "DisplayPropertyRole"; type: "number"; isReadonly: true }
        Property { name: "DecorationPropertyRole"; type: "number"; isReadonly: true }
        Property { name: "ToolTipPropertyRole"; type: "number"; isReadonly: true }
        Property { name: "StatusTipPropertyRole"; type: "number"; isReadonly: true }
        Property { name: "WhatsThisPropertyRole"; type: "number"; isReadonly: true }
        Property { name: "UserRole"; type: "number"; isReadonly: true }
        Property { name: "NoItemFlags"; type: "number"; isReadonly: true }
        Property { name: "ItemIsSelectable"; type: "number"; isReadonly: true }
        Property { name: "ItemIsEditable"; type: "number"; isReadonly: true }
        Property { name: "ItemIsDragEnabled"; type: "number"; isReadonly: true }
        Property { name: "ItemIsDropEnabled"; type: "number"; isReadonly: true }
        Property { name: "ItemIsUserCheckable"; type: "number"; isReadonly: true }
        Property { name: "ItemIsEnabled"; type: "number"; isReadonly: true }
        Property { name: "ItemIsAutoTristate"; type: "number"; isReadonly: true }
        Property { name: "ItemNeverHasChildren"; type: "number"; isReadonly: true }
        Property { name: "ItemIsUserTristate"; type: "number"; isReadonly: true }
        Property { name: "MatchExactly"; type: "number"; isReadonly: true }
        Property { name: "MatchContains"; type: "number"; isReadonly: true }
        Property { name: "MatchStartsWith"; type: "number"; isReadonly: true }
        Property { name: "MatchEndsWith"; type: "number"; isReadonly: true }
        Property { name: "MatchRegularExpression"; type: "number"; isReadonly: true }
        Property { name: "MatchWildcard"; type: "number"; isReadonly: true }
        Property { name: "MatchFixedString"; type: "number"; isReadonly: true }
        Property { name: "MatchTypeMask"; type: "number"; isReadonly: true }
        Property { name: "MatchCaseSensitive"; type: "number"; isReadonly: true }
        Property { name: "MatchWrap"; type: "number"; isReadonly: true }
        Property { name: "MatchRecursive"; type: "number"; isReadonly: true }
        Property { name: "NonModal"; type: "number"; isReadonly: true }
        Property { name: "WindowModal"; type: "number"; isReadonly: true }
        Property { name: "ApplicationModal"; type: "number"; isReadonly: true }
        Property { name: "NoTextInteraction"; type: "number"; isReadonly: true }
        Property { name: "TextSelectableByMouse"; type: "number"; isReadonly: true }
        Property { name: "TextSelectableByKeyboard"; type: "number"; isReadonly: true }
        Property { name: "LinksAccessibleByMouse"; type: "number"; isReadonly: true }
        Property { name: "LinksAccessibleByKeyboard"; type: "number"; isReadonly: true }
        Property { name: "TextEditable"; type: "number"; isReadonly: true }
        Property { name: "TextEditorInteraction"; type: "number"; isReadonly: true }
        Property { name: "TextBrowserInteraction"; type: "number"; isReadonly: true }
        Property { name: "MinimumSize"; type: "number"; isReadonly: true }
        Property { name: "PreferredSize"; type: "number"; isReadonly: true }
        Property { name: "MaximumSize"; type: "number"; isReadonly: true }
        Property { name: "MinimumDescent"; type: "number"; isReadonly: true }
        Property { name: "NSizeHints"; type: "number"; isReadonly: true }
        Property { name: "TouchPointUnknownState"; type: "number"; isReadonly: true }
        Property { name: "TouchPointPressed"; type: "number"; isReadonly: true }
        Property { name: "TouchPointMoved"; type: "number"; isReadonly: true }
        Property { name: "TouchPointStationary"; type: "number"; isReadonly: true }
        Property { name: "TouchPointReleased"; type: "number"; isReadonly: true }
        Property { name: "NoGesture"; type: "number"; isReadonly: true }
        Property { name: "GestureStarted"; type: "number"; isReadonly: true }
        Property { name: "GestureUpdated"; type: "number"; isReadonly: true }
        Property { name: "GestureFinished"; type: "number"; isReadonly: true }
        Property { name: "GestureCanceled"; type: "number"; isReadonly: true }
        Property { name: "TapGesture"; type: "number"; isReadonly: true }
        Property { name: "TapAndHoldGesture"; type: "number"; isReadonly: true }
        Property { name: "PanGesture"; type: "number"; isReadonly: true }
        Property { name: "PinchGesture"; type: "number"; isReadonly: true }
        Property { name: "SwipeGesture"; type: "number"; isReadonly: true }
        Property { name: "CustomGesture"; type: "number"; isReadonly: true }
        Property { name: "LastGestureType"; type: "number"; isReadonly: true }
        Property { name: "BeginNativeGesture"; type: "number"; isReadonly: true }
        Property { name: "EndNativeGesture"; type: "number"; isReadonly: true }
        Property { name: "PanNativeGesture"; type: "number"; isReadonly: true }
        Property { name: "ZoomNativeGesture"; type: "number"; isReadonly: true }
        Property { name: "SmartZoomNativeGesture"; type: "number"; isReadonly: true }
        Property { name: "RotateNativeGesture"; type: "number"; isReadonly: true }
        Property { name: "SwipeNativeGesture"; type: "number"; isReadonly: true }
        Property { name: "LogicalMoveStyle"; type: "number"; isReadonly: true }
        Property { name: "VisualMoveStyle"; type: "number"; isReadonly: true }
        Property { name: "PreciseTimer"; type: "number"; isReadonly: true }
        Property { name: "CoarseTimer"; type: "number"; isReadonly: true }
        Property { name: "VeryCoarseTimer"; type: "number"; isReadonly: true }
        Property { name: "Invalid"; type: "number"; isReadonly: true }
        Property { name: "NoScrollPhase"; type: "number"; isReadonly: true }
        Property { name: "ScrollBegin"; type: "number"; isReadonly: true }
        Property { name: "ScrollUpdate"; type: "number"; isReadonly: true }
        Property { name: "ScrollEnd"; type: "number"; isReadonly: true }
        Property { name: "ScrollMomentum"; type: "number"; isReadonly: true }
        Property { name: "MouseEventNotSynthesized"; type: "number"; isReadonly: true }
        Property { name: "MouseEventSynthesizedBySystem"; type: "number"; isReadonly: true }
        Property { name: "MouseEventSynthesizedByQt"; type: "number"; isReadonly: true }
        Property { name: "MouseEventSynthesizedByApplication"; type: "number"; isReadonly: true }
        Property { name: "NoMouseEventFlag"; type: "number"; isReadonly: true }
        Property { name: "MouseEventCreatedDoubleClick"; type: "number"; isReadonly: true }
        Property { name: "MouseEventFlagMask"; type: "number"; isReadonly: true }
        Property { name: "ChecksumIso3309"; type: "number"; isReadonly: true }
        Property { name: "ChecksumItuV41"; type: "number"; isReadonly: true }
        Property { name: "Unset"; type: "number"; isReadonly: true }
        Property { name: "Round"; type: "number"; isReadonly: true }
        Property { name: "Ceil"; type: "number"; isReadonly: true }
        Property { name: "Floor"; type: "number"; isReadonly: true }
        Property { name: "RoundPreferFloor"; type: "number"; isReadonly: true }
        Property { name: "PassThrough"; type: "number"; isReadonly: true }
        Property { name: "Undetermined"; type: "number"; isReadonly: true }
        Property { name: "Granted"; type: "number"; isReadonly: true }
        Property { name: "Denied"; type: "number"; isReadonly: true }
    }
    Component {
        name: "QtStyleHints"
        accessSemantics: "reference"
        prototype: "ObjectPrototype"
        isJavaScriptBuiltin: true
        Property { name: "objectName"; type: "string" }
        Method { name: "objectNameChanged"; isJavaScriptFunction: true }
    }
    Component {
        name: "RangeError"
        accessSemantics: "reference"
        prototype: "RangeErrorPrototype"
        isJavaScriptBuiltin: true
        Property { name: "stack"; type: "string" }
        Property { name: "fileName" }
        Property { name: "lineNumber" }
    }
    Component {
        name: "RangeErrorPrototype"
        accessSemantics: "reference"
        prototype: "ErrorPrototype"
        isJavaScriptBuiltin: true
        Property { name: "message"; type: "string" }
        Property { name: "name"; type: "string" }
        Method {
            name: "constructor"
            type: "RangeError"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
        }
        Method { name: "toString"; isJavaScriptFunction: true }
    }
    Component {
        name: "ReferenceError"
        accessSemantics: "reference"
        prototype: "ReferenceErrorPrototype"
        isJavaScriptBuiltin: true
        Property { name: "stack"; type: "string" }
        Property { name: "fileName" }
        Property { name: "lineNumber" }
    }
    Component {
        name: "ReferenceErrorPrototype"
        accessSemantics: "reference"
        prototype: "ErrorPrototype"
        isJavaScriptBuiltin: true
        Property { name: "message"; type: "string" }
        Property { name: "name"; type: "string" }
        Method {
            name: "constructor"
            type: "ReferenceError"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
        }
        Method { name: "toString"; isJavaScriptFunction: true }
    }
    Component {
        name: "Reflect"
        accessSemantics: "reference"
        prototype: "ObjectPrototype"
        isJavaScriptBuiltin: true
        Method {
            name: "apply"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
        }
        Method {
            name: "construct"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "defineProperty"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
        }
        Method {
            name: "deleteProperty"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "get"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "getOwnPropertyDescriptor"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "getPrototypeOf"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "has"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "isExtensible"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "ownKeys"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "preventExtensions"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "set"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
        }
        Method {
            name: "setPrototypeOf"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
    }
    Component {
        name: "RegExp"
        accessSemantics: "reference"
        prototype: "RegExpPrototype"
        isJavaScriptBuiltin: true
        Property { name: "lastIndex"; type: "number" }
    }
    Component {
        name: "RegExpPrototype"
        accessSemantics: "reference"
        prototype: "ObjectPrototype"
        isJavaScriptBuiltin: true
        Property { name: "flags"; type: "string" }
        Property { name: "global" }
        Property { name: "ignoreCase" }
        Property { name: "multiline" }
        Property { name: "source"; type: "string" }
        Property { name: "sticky" }
        Property { name: "unicode" }
        Method {
            name: "constructor"
            type: "RegExp"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "exec"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "test"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method { name: "toString"; isJavaScriptFunction: true }
        Method {
            name: "compile"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
    }
    Component {
        name: "Set"
        accessSemantics: "reference"
        prototype: "SetPrototype"
        isJavaScriptBuiltin: true
    }
    Component {
        name: "SetPrototype"
        accessSemantics: "reference"
        prototype: "ObjectPrototype"
        isJavaScriptBuiltin: true
        Property { name: "size"; type: "number" }
        Method { name: "constructor"; type: "Set"; isConstructor: true; isJavaScriptFunction: true }
        Method {
            name: "add"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method { name: "clear"; isJavaScriptFunction: true }
        Method {
            name: "delete"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method { name: "entries"; isJavaScriptFunction: true }
        Method {
            name: "forEach"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "has"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method { name: "keys"; isJavaScriptFunction: true }
        Method { name: "values"; isJavaScriptFunction: true }
    }
    Component {
        name: "SharedArrayBuffer"
        accessSemantics: "reference"
        prototype: "SharedArrayBufferPrototype"
        isJavaScriptBuiltin: true
    }
    Component {
        name: "SharedArrayBufferPrototype"
        accessSemantics: "reference"
        prototype: "ObjectPrototype"
        isJavaScriptBuiltin: true
        Property { name: "byteLength"; type: "number" }
        Method {
            name: "constructor"
            type: "SharedArrayBuffer"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "slice"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
    }
    Component {
        name: "String"
        accessSemantics: "reference"
        prototype: "StringPrototype"
        isJavaScriptBuiltin: true
        Property { name: "length"; type: "number"; isReadonly: true }
    }
    Component {
        name: "StringPrototype"
        accessSemantics: "reference"
        prototype: "ObjectPrototype"
        isJavaScriptBuiltin: true
        Property { name: "length"; type: "number"; isReadonly: true }
        Method {
            name: "constructor"
            type: "String"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
        }
        Method { name: "toString"; isJavaScriptFunction: true }
        Method { name: "valueOf"; isJavaScriptFunction: true }
        Method {
            name: "charAt"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "charCodeAt"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "codePointAt"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "concat"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "endsWith"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "indexOf"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "includes"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "lastIndexOf"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "localeCompare"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "match"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method { name: "normalize"; isJavaScriptFunction: true }
        Method {
            name: "padEnd"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "padStart"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "repeat"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "replace"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "search"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "slice"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "split"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "startsWith"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "substr"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method {
            name: "substring"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
        Method { name: "toLowerCase"; isJavaScriptFunction: true }
        Method { name: "toLocaleLowerCase"; isJavaScriptFunction: true }
        Method { name: "toUpperCase"; isJavaScriptFunction: true }
        Method { name: "toLocaleUpperCase"; isJavaScriptFunction: true }
        Method { name: "trim"; isJavaScriptFunction: true }
        Method { name: "arg"; isJavaScriptFunction: true }
    }
    Component {
        name: "SymbolPrototype"
        accessSemantics: "reference"
        prototype: "ObjectPrototype"
        isJavaScriptBuiltin: true
        Method {
            name: "constructor"
            type: "undefined"
            isConstructor: true
            isJavaScriptFunction: true
        }
        Method { name: "toString"; isJavaScriptFunction: true }
        Method { name: "valueOf"; isJavaScriptFunction: true }
    }
    Component {
        name: "SyntaxError"
        accessSemantics: "reference"
        prototype: "SyntaxErrorPrototype"
        isJavaScriptBuiltin: true
        Property { name: "stack"; type: "string" }
        Property { name: "fileName" }
        Property { name: "lineNumber" }
    }
    Component {
        name: "SyntaxErrorPrototype"
        accessSemantics: "reference"
        prototype: "ErrorPrototype"
        isJavaScriptBuiltin: true
        Property { name: "message"; type: "string" }
        Property { name: "name"; type: "string" }
        Method {
            name: "constructor"
            type: "SyntaxError"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
        }
        Method { name: "toString"; isJavaScriptFunction: true }
    }
    Component {
        name: "TypeError"
        accessSemantics: "reference"
        prototype: "TypeErrorPrototype"
        isJavaScriptBuiltin: true
        Property { name: "stack"; type: "string" }
        Property { name: "fileName" }
        Property { name: "lineNumber" }
    }
    Component {
        name: "TypeErrorPrototype"
        accessSemantics: "reference"
        prototype: "ErrorPrototype"
        isJavaScriptBuiltin: true
        Property { name: "message"; type: "string" }
        Property { name: "name"; type: "string" }
        Method {
            name: "constructor"
            type: "TypeError"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
        }
        Method { name: "toString"; isJavaScriptFunction: true }
    }
    Component {
        name: "URIError"
        accessSemantics: "reference"
        prototype: "URIErrorPrototype"
        isJavaScriptBuiltin: true
        Property { name: "stack"; type: "string" }
        Property { name: "fileName" }
        Property { name: "lineNumber" }
    }
    Component {
        name: "URIErrorPrototype"
        accessSemantics: "reference"
        prototype: "ErrorPrototype"
        isJavaScriptBuiltin: true
        Property { name: "message"; type: "string" }
        Property { name: "name"; type: "string" }
        Method {
            name: "constructor"
            type: "URIError"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
        }
        Method { name: "toString"; isJavaScriptFunction: true }
    }
    Component {
        name: "URL"
        accessSemantics: "reference"
        prototype: "URLPrototype"
        isJavaScriptBuiltin: true
    }
    Component {
        name: "URLPrototype"
        accessSemantics: "reference"
        prototype: "ObjectPrototype"
        isJavaScriptBuiltin: true
        Property { name: "hash" }
        Property { name: "host" }
        Property { name: "hostname" }
        Property { name: "href" }
        Property { name: "origin" }
        Property { name: "password" }
        Property { name: "pathname" }
        Property { name: "port" }
        Property { name: "protocol" }
        Property { name: "search" }
        Property { name: "searchParams" }
        Property { name: "username" }
        Method { name: "toString"; isJavaScriptFunction: true }
        Method { name: "toJSON"; isJavaScriptFunction: true }
    }
    Component {
        name: "URLSearchParams"
        accessSemantics: "reference"
        prototype: "URLSearchParamsPrototype"
        isJavaScriptBuiltin: true
    }
    Component {
        name: "URLSearchParamsPrototype"
        accessSemantics: "reference"
        prototype: "ObjectPrototype"
        isJavaScriptBuiltin: true
        Method { name: "toString"; isJavaScriptFunction: true }
        Method { name: "sort"; isJavaScriptFunction: true }
        Method { name: "append"; isJavaScriptFunction: true }
        Method { name: "delete"; isJavaScriptFunction: true }
        Method { name: "has"; isJavaScriptFunction: true }
        Method { name: "set"; isJavaScriptFunction: true }
        Method { name: "get"; isJavaScriptFunction: true }
        Method { name: "getAll"; isJavaScriptFunction: true }
        Method { name: "forEach"; isJavaScriptFunction: true }
        Method { name: "entries"; isJavaScriptFunction: true }
        Method { name: "keys"; isJavaScriptFunction: true }
        Method { name: "values"; isJavaScriptFunction: true }
    }
    Component {
        name: "Uint16Array"
        accessSemantics: "reference"
        prototype: "Uint16ArrayPrototype"
        isJavaScriptBuiltin: true
    }
    Component {
        name: "Uint16ArrayPrototype"
        accessSemantics: "reference"
        prototype: "IntrinsicTypedArrayPrototype"
        isJavaScriptBuiltin: true
        Property { name: "BYTES_PER_ELEMENT"; type: "number"; isReadonly: true }
        Method {
            name: "constructor"
            type: "Uint16Array"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
        }
    }
    Component {
        name: "Uint32Array"
        accessSemantics: "reference"
        prototype: "Uint32ArrayPrototype"
        isJavaScriptBuiltin: true
    }
    Component {
        name: "Uint32ArrayPrototype"
        accessSemantics: "reference"
        prototype: "IntrinsicTypedArrayPrototype"
        isJavaScriptBuiltin: true
        Property { name: "BYTES_PER_ELEMENT"; type: "number"; isReadonly: true }
        Method {
            name: "constructor"
            type: "Uint32Array"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
        }
    }
    Component {
        name: "Uint8Array"
        accessSemantics: "reference"
        prototype: "Uint8ArrayPrototype"
        isJavaScriptBuiltin: true
    }
    Component {
        name: "Uint8ArrayPrototype"
        accessSemantics: "reference"
        prototype: "IntrinsicTypedArrayPrototype"
        isJavaScriptBuiltin: true
        Property { name: "BYTES_PER_ELEMENT"; type: "number"; isReadonly: true }
        Method {
            name: "constructor"
            type: "Uint8Array"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
        }
    }
    Component {
        name: "Uint8ClampedArray"
        accessSemantics: "reference"
        prototype: "Uint8ClampedArrayPrototype"
        isJavaScriptBuiltin: true
    }
    Component {
        name: "Uint8ClampedArrayPrototype"
        accessSemantics: "reference"
        prototype: "IntrinsicTypedArrayPrototype"
        isJavaScriptBuiltin: true
        Property { name: "BYTES_PER_ELEMENT"; type: "number"; isReadonly: true }
        Method {
            name: "constructor"
            type: "Uint8ClampedArray"
            isConstructor: true
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
            Parameter {}
        }
    }
    Component {
        name: "WeakMap"
        accessSemantics: "reference"
        prototype: "WeakMapPrototype"
        isJavaScriptBuiltin: true
    }
    Component {
        name: "WeakMapPrototype"
        accessSemantics: "reference"
        prototype: "ObjectPrototype"
        isJavaScriptBuiltin: true
        Method { name: "constructor"; type: "WeakMap"; isConstructor: true; isJavaScriptFunction: true }
        Method {
            name: "delete"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "get"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "has"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "set"
            isJavaScriptFunction: true
            Parameter {}
            Parameter {}
        }
    }
    Component {
        name: "WeakSet"
        accessSemantics: "reference"
        prototype: "WeakSetPrototype"
        isJavaScriptBuiltin: true
    }
    Component {
        name: "WeakSetPrototype"
        accessSemantics: "reference"
        prototype: "ObjectPrototype"
        isJavaScriptBuiltin: true
        Method { name: "constructor"; type: "WeakSet"; isConstructor: true; isJavaScriptFunction: true }
        Method {
            name: "add"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "delete"
            isJavaScriptFunction: true
            Parameter {}
        }
        Method {
            name: "has"
            isJavaScriptFunction: true
            Parameter {}
        }
    }
    Component {
        name: "boolean"
        accessSemantics: "value"
        prototype: "BooleanPrototype"
        isJavaScriptBuiltin: true
    }
    Component {
        name: "function"
        accessSemantics: "reference"
        prototype: "FunctionPrototype"
        isJavaScriptBuiltin: true
        Property { name: "prototype"; type: "Object" }
        Property { name: "name"; type: "string"; isReadonly: true }
        Property { name: "length"; type: "number"; isReadonly: true }
    }
    Component {
        name: "number"
        accessSemantics: "value"
        prototype: "NumberPrototype"
        isJavaScriptBuiltin: true
    }
    Component {
        name: "object"
        accessSemantics: "reference"
        prototype: "ObjectPrototype"
        isJavaScriptBuiltin: true
    }
    Component {
        name: "string"
        accessSemantics: "value"
        prototype: "StringPrototype"
        isJavaScriptBuiltin: true
    }
    Component {
        name: "symbol"
        accessSemantics: "value"
        prototype: "SymbolPrototype"
        isJavaScriptBuiltin: true
    }
    Component { name: "undefined"; accessSemantics: "value"; isJavaScriptBuiltin: true }
}
