import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "theme"
import "ribbon"
import "desktop"
import "dock"
import "status"

Item {
    id: root
    
    // ViewModel will be injected from C++
    property var viewModel: null
    
    ColumnLayout {
        anchors.fill: parent
        spacing: 0
        
        // Quick Access Toolbar and Ribbon
        Item {
            Layout.fillWidth: true
            Layout.preferredHeight: AppTheme.ribbon.height + AppTheme.ribbon.tabHeight
            
            ColumnLayout {
                anchors.fill: parent
                spacing: 0
                
                // Quick Access Toolbar
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: AppTheme.ribbon.tabHeight
                    color: Colors.ribbonBackground
                    border.color: Colors.border
                    border.width: AppTheme.border.widthThin
                    
                    RowLayout {
                        anchors.fill: parent
                        anchors.margins: AppTheme.spacing.sm
                        
                        // Quick Access Button (Customize Ribbon)
                        Rectangle {
                            Layout.preferredWidth: 24
                            Layout.preferredHeight: 24
                            radius: 4
                            color: quickAccessMouseArea.containsMouse ? Colors.hover : "transparent"
                            border.color: Colors.border
                            border.width: 1
                            
                            Text {
                                anchors.centerIn: parent
                                text: "⚙"
                                font.pixelSize: 12
                                color: Colors.onBackground
                            }
                            
                            MouseArea {
                                id: quickAccessMouseArea
                                anchors.fill: parent
                                hoverEnabled: true
                                onClicked: {
                                    // Show customize ribbon dialog
                                    if (viewModel) {
                                        viewModel.showCustomizeRibbonDialog()
                                    }
                                }
                            }
                        }
                        
                        Item { Layout.fillWidth: true } // Spacer
                        
                        // Window controls would go here
                    }
                }
                
                // Ribbon Interface
                RibbonView {
                    id: ribbonView
                    Layout.fillWidth: true
                    Layout.preferredHeight: AppTheme.ribbon.height
                    viewModel: root.viewModel ? root.viewModel.ribbonViewModel : null
                }
            }
        }
        
        // Desktop Tabs
        DesktopTabs {
            id: desktopTabs
            Layout.fillWidth: true
            Layout.preferredHeight: AppTheme.tab.height
            viewModel: root.viewModel
        }
        
        // Main Content Area with Dock System
        DockArea {
            id: mainDockArea
            Layout.fillWidth: true
            Layout.fillHeight: true
            viewModel: root.viewModel
        }
        
        // Status Bar
        StatusBar {
            id: statusBar
            Layout.fillWidth: true
            Layout.preferredHeight: AppTheme.status.height
            viewModel: root.viewModel
        }
    }
    
    // Connections to ViewModel
    Connections {
        target: viewModel
        
        function onWindowTitleChanged(title) {
            if (root.Window.window) {
                root.Window.window.title = title
            }
        }
        
        function onShowMessage(message, type) {
            // Show status message
            statusBar.showMessage(message, type)
        }
    }
    
    // Initialize when ViewModel is available
    onViewModelChanged: {
        if (viewModel) {
            console.log("MainWindow ViewModel connected")
            // Initialize components
            ribbonView.initialize()
            desktopTabs.initialize()
            mainDockArea.initialize()
            statusBar.initialize()
        }
    }
}
