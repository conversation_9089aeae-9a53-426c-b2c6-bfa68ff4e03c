#include "view_models/main_window_view_model.h"

#include <QDateTime>
#include <QTimer>

#include "services/dock_manager.h"
#include "services/ribbon_config_service.h"
#include "view_models/ribbon_view_model.h"
#include "views/trace_dock_widget.h"

namespace CANoeLite::ViewModels {

MainWindowViewModel::MainWindowViewModel(std::shared_ptr<Services::DockManager> dock_manager,
                                         std::shared_ptr<Services::RibbonConfigService> ribbon_config_service,
                                         QObject* parent)
    : Core::IViewModel(parent),
      dock_manager_(dock_manager),
      ribbon_config_service_(ribbon_config_service),
      window_title_("CANoeLite"),
      status_text_("Ready"),
      can_bitrate_("500 kBit/s"),
      clock_time_(QDateTime::currentDateTime().toString("hh:mm:ss")) {
    // Create ribbon view model
    ribbon_view_model_ = std::make_shared<RibbonViewModel>(ribbon_config_service_, this);

    setup_commands();

    // Register commands with ribbon
    ribbon_view_model_->register_command("show_trace_view", show_trace_view_command_);
    ribbon_view_model_->register_command("show_statistics", show_statistics_command_);
    ribbon_view_model_->register_command("file_new", file_new_command_);
    ribbon_view_model_->register_command("file_open", file_open_command_);
    ribbon_view_model_->register_command("file_save", file_save_command_);

    // Setup clock timer
    auto* timer = new QTimer(this);
    connect(timer, &QTimer::timeout, this, &MainWindowViewModel::update_clock);
    timer->start(1000);  // Update every second
}

void MainWindowViewModel::setup_commands() {
    show_trace_view_command_ = std::make_shared<Core::RelayCommand>([this]() { show_trace_view(); });
    show_statistics_command_ = std::make_shared<Core::RelayCommand>([this]() { show_statistics(); });
    file_new_command_ = std::make_shared<Core::RelayCommand>([this]() { file_new(); });
    file_open_command_ = std::make_shared<Core::RelayCommand>([this]() { file_open(); });
    file_save_command_ = std::make_shared<Core::RelayCommand>([this]() { file_save(); });
}

void MainWindowViewModel::set_window_title(const QString& title) {
    if (window_title_ != title) {
        window_title_ = title;
        emit window_title_changed(title);
        emit property_changed("window_title", title);
    }
}

void MainWindowViewModel::set_status_text(const QString& text) {
    if (status_text_ != text) {
        status_text_ = text;
        emit status_text_changed(text);
        emit property_changed("status_text", text);
    }
}

void MainWindowViewModel::set_can_bitrate(const QString& bitrate) {
    if (can_bitrate_ != bitrate) {
        can_bitrate_ = bitrate;
        emit can_bitrate_changed(bitrate);
        emit property_changed("can_bitrate", bitrate);
    }
}

void MainWindowViewModel::update_clock() {
    QString new_time = QDateTime::currentDateTime().toString("hh:mm:ss");
    if (clock_time_ != new_time) {
        clock_time_ = new_time;
        emit clock_time_changed(new_time);
        emit property_changed("clock_time", new_time);
    }
}

void MainWindowViewModel::show_trace_view() {
    auto* trace_widget = new Views::TraceDockWidget();
    dock_manager_->create_dock_widget("trace_view", "Trace View", trace_widget);
    set_status_text("Trace view opened");
}

void MainWindowViewModel::show_statistics() {
    // Placeholder for statistics view
    set_status_text("Statistics view not implemented yet");
}

void MainWindowViewModel::file_new() {
    set_status_text("New file created");
}

void MainWindowViewModel::file_open() {
    set_status_text("File open dialog would appear here");
}

void MainWindowViewModel::file_save() {
    set_status_text("File saved");
}

}  // namespace CANoeLite::ViewModels
