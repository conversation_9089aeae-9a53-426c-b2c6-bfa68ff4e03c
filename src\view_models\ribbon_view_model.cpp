#include "view_models/ribbon_view_model.h"

#include "services/ribbon_config_service.h"

namespace CANoeLite::ViewModels {

RibbonViewModel::RibbonViewModel(std::shared_ptr<Services::RibbonConfigService> ribbon_config_service,
                                 QObject* parent)
    : Core::IViewModel(parent),
      ribbon_config_service_(ribbon_config_service),
      quick_access_visible_(true),
      current_tab_("File") {}

QVariantMap RibbonViewModel::ribbon_data() const {
    return ribbon_config_service_->get_ribbon_data();
}

std::shared_ptr<Core::ICommand> RibbonViewModel::get_command(const QString& action) const {
    return commands_.value(action);
}

void RibbonViewModel::register_command(const QString& action, std::shared_ptr<Core::ICommand> command) {
    commands_[action] = command;
}

void RibbonViewModel::set_quick_access_visible(bool visible) {
    if (quick_access_visible_ != visible) {
        quick_access_visible_ = visible;
        emit quick_access_visibility_changed(visible);
        emit property_changed("quick_access_visible", visible);
    }
}

void RibbonViewModel::set_current_tab(const QString& tab) {
    if (current_tab_ != tab) {
        current_tab_ = tab;
        emit current_tab_changed(tab);
        emit property_changed("current_tab", tab);
    }
}

void RibbonViewModel::execute_action(const QString& action) {
    auto command = get_command(action);
    if (command && command->can_execute()) {
        command->execute();
        emit action_executed(action);
    }
}

}  // namespace CANoeLite::ViewModels
