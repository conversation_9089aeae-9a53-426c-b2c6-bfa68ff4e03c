import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qqmllocalstorage_p.h"
        name: "QQmlLocalStorage"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtQuick.LocalStorage/LocalStorage 2.0",
            "QtQuick.LocalStorage/LocalStorage 6.0"
        ]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [512, 1536]
        Method { name: "openDatabaseSync"; isJavaScriptFunction: true }
    }
}
