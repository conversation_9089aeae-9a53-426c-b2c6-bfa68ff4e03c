#pragma once

#include <QMainWindow>
#include <memory>

namespace CANoeLite::ViewModels {
class MainWindowViewModel;
}

namespace CANoeLite::Views {

class RibbonView;
class DesktopTabsWidget;
class StatusBarView;

/**
 * @brief Main window view
 */
class MainWindow : public QMainWindow {
    Q_OBJECT

public:
    explicit MainWindow(std::shared_ptr<ViewModels::MainWindowViewModel> view_model, QWidget* parent = nullptr);
    ~MainWindow() override = default;

protected:
    void closeEvent(QCloseEvent* event) override;

private slots:
    void on_window_title_changed(const QString& title);

private:
    void setup_ui();
    void setup_connections();

    std::shared_ptr<ViewModels::MainWindowViewModel> view_model_;
    RibbonView* ribbon_view_;
    DesktopTabsWidget* desktop_tabs_;
    StatusBarView* status_bar_view_;
};

}  // namespace CANoeLite::Views
