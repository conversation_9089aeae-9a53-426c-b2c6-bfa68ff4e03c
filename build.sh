#!/bin/bash

# Set up UCRT64 environment
export PATH="/c/msys64/ucrt64/bin:$PATH"
export PKG_CONFIG_PATH="/c/msys64/ucrt64/lib/pkgconfig"

echo "🔧 Building CANoeLite with UCRT64 toolchain..."
echo "📍 Using CMAKE: $(which cmake)"

# Clean previous build if requested
if [[ "$1" == "clean" ]]; then
    echo "🧹 Cleaning previous build..."
    rm -rf build
fi

cmake -B build -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Release .

cmake --build build -j$(nproc)

# Check if build was successful
if [ $? -ne 0 ]; then
    echo "❌ Build failed!"
    exit 1
fi

cd build

# Đường dẫn đến file .exe
EXE="./src/CANoeLite.exe"

# Check if the executable exists
if [[ ! -f "$EXE" ]]; then
    echo "❌ Không tìm thấy $EXE. Vui lòng chạy script từ thư mục chứa CANoeLite.exe"
    exit 1
fi

DEST_DIR="."  # Destination folder (same as EXE)

echo "🧩 Phân tích thư viện phụ thuộc của $EXE..."

# Copy dependent DLLs from ldd output
ldd "$EXE" | grep "ucrt64" | awk '{print $3}' | while read dll; do
    if [[ -f "$dll" ]]; then
        echo "✅ Copy: $(basename "$dll")"
        cp -u "$dll" "$DEST_DIR/"
    else
        echo "❌ Không tìm thấy: $dll"
    fi
done

# Copy Qt platform plugins to both locations
PLUGIN_SRC="/c/msys64/ucrt64/share/qt6/plugins/platforms/qwindows.dll"
PLUGIN_DEST="$DEST_DIR/platforms"
PLUGIN_DEST_SRC="./src/platforms"

echo "🧩 Kiểm tra Qt platform plugin..."
if [[ -f "$PLUGIN_SRC" ]]; then
    mkdir -p "$PLUGIN_DEST"
    mkdir -p "$PLUGIN_DEST_SRC"
    cp -u "$PLUGIN_SRC" "$PLUGIN_DEST/"
    cp -u "$PLUGIN_SRC" "$PLUGIN_DEST_SRC/"
    echo "✅ Đã copy plugin: platforms/qwindows.dll"
    echo "✅ Đã copy plugin: src/platforms/qwindows.dll"
else
    echo "❌ Không tìm thấy Qt platform plugin: $PLUGIN_SRC"
    echo "👉 Hãy kiểm tra đường dẫn Qt hoặc dùng windeployqt nếu cần"
fi

# Deploy Qt dependencies using windeployqt
echo "🧩 Deploying Qt dependencies..."
echo "📍 Checking for: $(pwd)/src/CANoeLite.exe"
if [ -f "src/CANoeLite.exe" ]; then
    cd src
    /c/msys64/ucrt64/bin/windeployqt6.exe --quick --qml CANoeLite.exe

    # Manually copy essential QML modules that windeployqt might miss
    QML_SRC="/c/msys64/ucrt64/share/qt6/qml"
    if [ -d "$QML_SRC" ]; then
        echo "🧩 Copying essential QML modules..."
        mkdir -p qml
        cp -r "$QML_SRC/QtQuick" qml/ 2>/dev/null || true
        cp "$QML_SRC/builtins.qmltypes" qml/ 2>/dev/null || true
        cp "$QML_SRC/jsroot.qmltypes" qml/ 2>/dev/null || true
        echo "✅ Essential QML modules copied"
    fi

    cd ..
    echo "✅ Qt dependencies deployed"
else
    echo "❌ CANoeLite.exe not found for deployment"
fi

# Copy DLLs to src directory as well
echo "🧩 Copy DLLs to src directory..."
cp -u *.dll ./src/ 2>/dev/null || true

echo "✅ Hoàn tất copy DLL và plugin."

echo ""
echo "🎉 Build thành công!"
echo "📁 Executable: build/src/CANoeLite.exe"
echo "🚀 Chạy ứng dụng: cd build/src && ./CANoeLite.exe"
echo ""
