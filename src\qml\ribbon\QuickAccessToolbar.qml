import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../theme"
import "../components"

Rectangle {
    id: root
    
    property var viewModel: null
    property var quickAccessButtons: ["file_new", "file_open", "file_save"]
    
    color: Colors.ribbonBackground
    border.color: Colors.border
    border.width: AppTheme.border.widthThin
    
    RowLayout {
        anchors.fill: parent
        anchors.margins: AppTheme.spacing.sm
        spacing: AppTheme.spacing.xs
        
        Repeater {
            model: root.quickAccessButtons
            
            ModernButton {
                Layout.preferredWidth: 24
                Layout.preferredHeight: 24
                
                text: ""
                iconSource: getIconForAction(modelData)
                iconSize: 16
                
                backgroundColor: "transparent"
                backgroundColorHover: Colors.ribbonButtonHover
                
                onClicked: {
                    if (root.viewModel) {
                        root.viewModel.executeCommand(modelData)
                    }
                }
            }
        }
        
        Item { Layout.fillWidth: true } // Spacer
    }
    
    function getIconForAction(action) {
        switch (action) {
            case "file_new": return "qrc:/icons/new.png"
            case "file_open": return "qrc:/icons/open.png"
            case "file_save": return "qrc:/icons/save.png"
            default: return ""
        }
    }
}
