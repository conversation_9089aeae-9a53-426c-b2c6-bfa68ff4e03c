import QtQuick 2.15
import QtGraphicalEffects 1.15
import "../theme"

Rectangle {
    id: root
    
    // Custom properties
    property alias title: titleText.text
    property alias content: contentLoader.sourceComponent
    property bool elevated: true
    property color backgroundColor: Colors.surface
    property color borderColor: Colors.border
    property int elevation: AppTheme.elevation.sm
    
    color: backgroundColor
    radius: AppTheme.radius.md
    border.color: borderColor
    border.width: AppTheme.border.widthThin
    
    // Shadow effect for elevation
    layer.enabled: elevated
    layer.effect: DropShadow {
        horizontalOffset: elevation > 2 ? AppTheme.shadow.elevation4.offsetX : AppTheme.shadow.elevation2.offsetX
        verticalOffset: elevation > 2 ? AppTheme.shadow.elevation4.offsetY : AppTheme.shadow.elevation2.offsetY
        radius: elevation > 2 ? AppTheme.shadow.elevation4.blurRadius : AppTheme.shadow.elevation2.blurRadius
        color: elevation > 2 ? AppTheme.shadow.elevation4.color : AppTheme.shadow.elevation2.color
    }
    
    Column {
        anchors.fill: parent
        anchors.margins: AppTheme.spacing.md
        
        // Title bar
        Rectangle {
            id: titleBar
            width: parent.width
            height: title !== "" ? AppTheme.spacing.xl : 0
            color: "transparent"
            visible: title !== ""
            
            Text {
                id: titleText
                anchors.left: parent.left
                anchors.verticalCenter: parent.verticalCenter
                font.family: AppTheme.typography.primaryFont
                font.pixelSize: AppTheme.typography.titleMedium
                font.weight: AppTheme.typography.weightMedium
                color: Colors.onSurface
            }
            
            Rectangle {
                anchors.bottom: parent.bottom
                width: parent.width
                height: 1
                color: Colors.separator
            }
        }
        
        // Content area
        Item {
            width: parent.width
            height: parent.height - titleBar.height
            
            Loader {
                id: contentLoader
                anchors.fill: parent
                anchors.topMargin: title !== "" ? AppTheme.spacing.sm : 0
            }
        }
    }
}
