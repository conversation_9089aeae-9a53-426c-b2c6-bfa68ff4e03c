import QtQuick 2.15
import QtQuick.Controls 2.15
import QtGraphicalEffects 1.15
import "../theme"

Rectangle {
    id: root
    
    property string title: "Dock Widget"
    property alias content: contentLoader.sourceComponent
    property bool floating: false
    property bool resizable: true
    
    signal closeRequested()
    signal floatRequested()
    signal dockRequested()
    
    color: Colors.dockBackground
    border.color: Colors.border
    border.width: AppTheme.border.widthThin
    radius: AppTheme.dock.borderRadius
    
    // Shadow effect
    layer.enabled: floating
    layer.effect: DropShadow {
        horizontalOffset: AppTheme.shadow.elevation4.offsetX
        verticalOffset: AppTheme.shadow.elevation4.offsetY
        radius: AppTheme.shadow.elevation4.blurRadius
        color: AppTheme.shadow.elevation4.color
    }
    
    Column {
        anchors.fill: parent
        
        // Title bar
        Rectangle {
            id: titleBar
            width: parent.width
            height: AppTheme.dock.titleBarHeight
            color: floating ? Colors.dockTitleBarActive : Colors.dockTitleBar
            radius: AppTheme.dock.borderRadius
            
            RowLayout {
                anchors.fill: parent
                anchors.margins: AppTheme.spacing.sm
                
                Text {
                    text: root.title
                    font.family: AppTheme.typography.dock.fontFamily
                    font.pixelSize: AppTheme.typography.dock.titleFontSize
                    font.weight: AppTheme.typography.dock.titleFontWeight
                    color: floating ? Colors.onPrimary : Colors.onSurface
                    Layout.fillWidth: true
                }
                
                // Float/Dock button
                Rectangle {
                    Layout.preferredWidth: 20
                    Layout.preferredHeight: 20
                    radius: 4
                    color: floatMouseArea.containsMouse ? Colors.hover : "transparent"
                    
                    Text {
                        anchors.centerIn: parent
                        text: floating ? "⚓" : "🗗"
                        font.pixelSize: 12
                        color: floating ? Colors.onPrimary : Colors.onSurface
                    }
                    
                    MouseArea {
                        id: floatMouseArea
                        anchors.fill: parent
                        hoverEnabled: true
                        onClicked: {
                            if (floating) {
                                root.dockRequested()
                            } else {
                                root.floatRequested()
                            }
                        }
                    }
                }
                
                // Close button
                Rectangle {
                    Layout.preferredWidth: 20
                    Layout.preferredHeight: 20
                    radius: 4
                    color: closeMouseArea.containsMouse ? Colors.error : "transparent"
                    
                    Text {
                        anchors.centerIn: parent
                        text: "×"
                        font.pixelSize: 12
                        font.weight: Font.Bold
                        color: closeMouseArea.containsMouse ? Colors.onPrimary : 
                               floating ? Colors.onPrimary : Colors.onSurface
                    }
                    
                    MouseArea {
                        id: closeMouseArea
                        anchors.fill: parent
                        hoverEnabled: true
                        onClicked: root.closeRequested()
                    }
                }
            }
        }
        
        // Content area
        Rectangle {
            width: parent.width
            height: parent.height - titleBar.height
            color: Colors.surface
            
            Loader {
                id: contentLoader
                anchors.fill: parent
                anchors.margins: AppTheme.spacing.sm
            }
        }
    }
    
    // Resize handles (if resizable)
    Rectangle {
        visible: resizable
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        width: 12
        height: 12
        color: "transparent"
        
        Rectangle {
            anchors.right: parent.right
            anchors.bottom: parent.bottom
            width: 8
            height: 8
            color: Colors.border
            
            MouseArea {
                anchors.fill: parent
                cursorShape: Qt.SizeFDiagCursor
                // Resize logic would go here
            }
        }
    }
}
