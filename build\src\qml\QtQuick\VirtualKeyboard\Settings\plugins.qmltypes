import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qquickvirtualkeyboardsettings_p.h"
        name: "QtVirtualKeyboard::QQuickVirtualKeyboardSettings"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtQuick.VirtualKeyboard.Settings/VirtualKeyboardSettings 1.0",
            "QtQuick.VirtualKeyboard.Settings/VirtualKeyboardSettings 6.0",
            "QtQuick.VirtualKeyboard.Settings/VirtualKeyboardSettings 6.1",
            "QtQuick.VirtualKeyboard.Settings/VirtualKeyboardSettings 6.6",
            "QtQuick.VirtualKeyboard.Settings/VirtualKeyboardSettings 6.8",
            "QtQuick.VirtualKeyboard.Settings/VirtualKeyboardSettings 6.9"
        ]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [256, 1536, 1537, 1542, 1544, 1545]
        Property {
            name: "style"
            type: "QUrl"
            read: "style"
            notify: "styleChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "layoutPath"
            type: "QUrl"
            read: "layoutPath"
            write: "setLayoutPath"
            notify: "layoutPathChanged"
            index: 1
        }
        Property {
            name: "styleName"
            type: "QString"
            read: "styleName"
            write: "setStyleName"
            notify: "styleNameChanged"
            index: 2
        }
        Property {
            name: "locale"
            type: "QString"
            read: "locale"
            write: "setLocale"
            notify: "localeChanged"
            index: 3
        }
        Property {
            name: "availableLocales"
            type: "QStringList"
            read: "availableLocales"
            notify: "availableLocalesChanged"
            index: 4
            isReadonly: true
        }
        Property {
            name: "activeLocales"
            type: "QStringList"
            read: "activeLocales"
            write: "setActiveLocales"
            notify: "activeLocalesChanged"
            index: 5
        }
        Property {
            name: "wordCandidateList"
            type: "QQuickWordCandidateListSettings"
            isPointer: true
            read: "wordCandidateList"
            index: 6
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "fullScreenMode"
            type: "bool"
            read: "fullScreenMode"
            write: "setFullScreenMode"
            notify: "fullScreenModeChanged"
            index: 7
        }
        Property {
            name: "userDataPath"
            revision: 1537
            type: "QString"
            read: "userDataPath"
            write: "setUserDataPath"
            notify: "userDataPathChanged"
            index: 8
        }
        Property {
            name: "hwrTimeoutForAlphabetic"
            revision: 1537
            type: "int"
            read: "hwrTimeoutForAlphabetic"
            write: "setHwrTimeoutForAlphabetic"
            notify: "hwrTimeoutForAlphabeticChanged"
            index: 9
        }
        Property {
            name: "hwrTimeoutForCjk"
            revision: 1537
            type: "int"
            read: "hwrTimeoutForCjk"
            write: "setHwrTimeoutForCjk"
            notify: "hwrTimeoutForCjkChanged"
            index: 10
        }
        Property {
            name: "inputMethodHints"
            revision: 1537
            type: "Qt::InputMethodHints"
            read: "inputMethodHints"
            write: "setInputMethodHints"
            notify: "inputMethodHintsChanged"
            index: 11
        }
        Property {
            name: "handwritingModeDisabled"
            revision: 1537
            type: "bool"
            read: "isHandwritingModeDisabled"
            write: "setHandwritingModeDisabled"
            notify: "handwritingModeDisabledChanged"
            index: 12
        }
        Property {
            name: "defaultInputMethodDisabled"
            revision: 1537
            type: "bool"
            read: "isDefaultInputMethodDisabled"
            write: "setDefaultInputMethodDisabled"
            notify: "defaultInputMethodDisabledChanged"
            index: 13
        }
        Property {
            name: "defaultDictionaryDisabled"
            revision: 1537
            type: "bool"
            read: "isDefaultDictionaryDisabled"
            write: "setDefaultDictionaryDisabled"
            notify: "defaultDictionaryDisabledChanged"
            index: 14
        }
        Property {
            name: "visibleFunctionKeys"
            revision: 1542
            type: "QtVirtualKeyboard::KeyboardFunctionKeys"
            read: "visibleFunctionKeys"
            write: "setVisibleFunctionKeys"
            notify: "visibleFunctionKeysChanged"
            index: 15
        }
        Property {
            name: "closeOnReturn"
            revision: 1544
            type: "bool"
            read: "closeOnReturn"
            write: "setCloseOnReturn"
            notify: "closeOnReturnChanged"
            index: 16
        }
        Property {
            name: "keySoundVolume"
            revision: 1545
            type: "double"
            read: "keySoundVolume"
            write: "setKeySoundVolume"
            notify: "keySoundVolumeChanged"
            index: 17
        }
        Signal { name: "styleChanged" }
        Signal { name: "styleNameChanged" }
        Signal { name: "localeChanged" }
        Signal { name: "availableLocalesChanged" }
        Signal { name: "activeLocalesChanged" }
        Signal { name: "layoutPathChanged" }
        Signal { name: "fullScreenModeChanged" }
        Signal { name: "userDataPathChanged"; revision: 1537 }
        Signal { name: "userDataReset"; revision: 1537 }
        Signal { name: "hwrTimeoutForAlphabeticChanged"; revision: 1537 }
        Signal { name: "hwrTimeoutForCjkChanged"; revision: 1537 }
        Signal { name: "inputMethodHintsChanged"; revision: 1537 }
        Signal { name: "handwritingModeDisabledChanged"; revision: 1537 }
        Signal { name: "defaultInputMethodDisabledChanged"; revision: 1537 }
        Signal { name: "defaultDictionaryDisabledChanged"; revision: 1537 }
        Signal { name: "visibleFunctionKeysChanged"; revision: 1542 }
        Signal { name: "closeOnReturnChanged"; revision: 1544 }
        Signal { name: "keySoundVolumeChanged"; revision: 1545 }
        Method {
            name: "convertVolume"
            revision: 1545
            type: "double"
            isMethodConstant: true
            Parameter { name: "volume"; type: "double" }
        }
    }
    Component {
        file: "private/qquickvirtualkeyboardsettings_p.h"
        name: "QtVirtualKeyboard::QQuickWordCandidateListSettings"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "autoHideDelay"
            type: "int"
            read: "autoHideDelay"
            write: "setAutoHideDelay"
            notify: "autoHideDelayChanged"
            index: 0
        }
        Property {
            name: "alwaysVisible"
            type: "bool"
            read: "alwaysVisible"
            write: "setAlwaysVisible"
            notify: "alwaysVisibleChanged"
            index: 1
        }
        Property {
            name: "autoCommitWord"
            type: "bool"
            read: "autoCommitWord"
            write: "setAutoCommitWord"
            notify: "autoCommitWordChanged"
            index: 2
        }
        Signal { name: "autoHideDelayChanged" }
        Signal { name: "alwaysVisibleChanged" }
        Signal { name: "autoCommitWordChanged" }
    }
}
