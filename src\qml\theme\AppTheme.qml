pragma Singleton
import QtQuick 2.15

QtObject {
    // Import theme components
    readonly property alias colors: Colors
    readonly property alias typography: Typography
    readonly property alias animations: Animations
    
    // Spacing and Sizing
    readonly property QtObject spacing: QtObject {
        readonly property int xs: 4
        readonly property int sm: 8
        readonly property int md: 16
        readonly property int lg: 24
        readonly property int xl: 32
        readonly property int xxl: 48
    }
    
    readonly property QtObject radius: QtObject {
        readonly property int none: 0
        readonly property int xs: 2
        readonly property int sm: 4
        readonly property int md: 8
        readonly property int lg: 12
        readonly property int xl: 16
        readonly property int full: 9999
    }
    
    readonly property QtObject elevation: QtObject {
        readonly property int none: 0
        readonly property int xs: 1
        readonly property int sm: 2
        readonly property int md: 4
        readonly property int lg: 8
        readonly property int xl: 12
        readonly property int xxl: 16
    }
    
    // Component Dimensions
    readonly property QtObject button: QtObject {
        readonly property int heightSmall: 28
        readonly property int heightMedium: 36
        readonly property int heightLarge: 44
        readonly property int minWidth: 64
        readonly property int paddingHorizontal: spacing.md
        readonly property int paddingVertical: spacing.sm
        readonly property int borderRadius: radius.sm
    }
    
    readonly property QtObject ribbon: QtObject {
        readonly property int height: 120
        readonly property int tabHeight: 32
        readonly property int panelMinWidth: 80
        readonly property int buttonSize: 64
        readonly property int buttonSpacing: spacing.sm
        readonly property int panelPadding: spacing.md
        readonly property int borderRadius: radius.sm
    }
    
    readonly property QtObject dock: QtObject {
        readonly property int titleBarHeight: 28
        readonly property int minWidth: 200
        readonly property int minHeight: 150
        readonly property int splitterSize: 4
        readonly property int borderRadius: radius.sm
        readonly property int shadowElevation: elevation.md
    }
    
    readonly property QtObject tab: QtObject {
        readonly property int height: 32
        readonly property int minWidth: 120
        readonly property int maxWidth: 200
        readonly property int closeButtonSize: 16
        readonly property int borderRadius: radius.sm
    }
    
    readonly property QtObject status: QtObject {
        readonly property int height: 24
        readonly property int panelMinWidth: 100
        readonly property int separatorWidth: 1
        readonly property int padding: spacing.sm
    }
    
    // Material Design Shadows
    readonly property QtObject shadow: QtObject {
        readonly property QtObject elevation1: QtObject {
            readonly property real offsetX: 0
            readonly property real offsetY: 1
            readonly property real blurRadius: 3
            readonly property color color: "#1F000000"
        }
        
        readonly property QtObject elevation2: QtObject {
            readonly property real offsetX: 0
            readonly property real offsetY: 2
            readonly property real blurRadius: 6
            readonly property color color: "#24000000"
        }
        
        readonly property QtObject elevation4: QtObject {
            readonly property real offsetX: 0
            readonly property real offsetY: 4
            readonly property real blurRadius: 12
            readonly property color color: "#29000000"
        }
        
        readonly property QtObject elevation8: QtObject {
            readonly property real offsetX: 0
            readonly property real offsetY: 8
            readonly property real blurRadius: 24
            readonly property color color: "#2E000000"
        }
    }
    
    // Border Styles
    readonly property QtObject border: QtObject {
        readonly property int widthThin: 1
        readonly property int widthMedium: 2
        readonly property int widthThick: 3
        readonly property color colorDefault: colors.border
        readonly property color colorLight: colors.borderLight
        readonly property color colorDark: colors.borderDark
    }
    
    // Icon Sizes
    readonly property QtObject icon: QtObject {
        readonly property int xs: 12
        readonly property int sm: 16
        readonly property int md: 20
        readonly property int lg: 24
        readonly property int xl: 32
        readonly property int xxl: 48
    }
}
