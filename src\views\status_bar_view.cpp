#include "views/status_bar_view.h"

#include "view_models/main_window_view_model.h"

namespace CANoeLite::Views {

StatusBarView::StatusBarView(std::shared_ptr<ViewModels::MainWindowViewModel> view_model, QWidget* parent)
    : QStatusBar(parent), view_model_(view_model) {
    setup_ui();
    setup_connections();
}

void StatusBarView::setup_ui() {
    // Status text (left side)
    status_label_ = new QLabel(view_model_->status_text());
    addWidget(status_label_, 1);  // Stretch factor 1

    // Progress bar (middle)
    progress_bar_ = new QProgressBar();
    progress_bar_->setVisible(false);  // Hidden by default
    progress_bar_->setMaximumWidth(200);
    addWidget(progress_bar_);

    // CAN bitrate (right side)
    can_bitrate_label_ = new QLabel("CAN: " + view_model_->can_bitrate());
    can_bitrate_label_->setMinimumWidth(100);
    addPermanentWidget(can_bitrate_label_);

    // Clock (far right)
    clock_label_ = new QLabel(view_model_->clock_time());
    clock_label_->setMinimumWidth(80);
    addPermanentWidget(clock_label_);
}

void StatusBarView::setup_connections() {
    connect(view_model_.get(), &ViewModels::MainWindowViewModel::status_text_changed, this,
            &StatusBarView::on_status_text_changed);
    connect(view_model_.get(), &ViewModels::MainWindowViewModel::can_bitrate_changed, this,
            &StatusBarView::on_can_bitrate_changed);
    connect(view_model_.get(), &ViewModels::MainWindowViewModel::clock_time_changed, this,
            &StatusBarView::on_clock_time_changed);
}

void StatusBarView::on_status_text_changed(const QString& text) {
    status_label_->setText(text);
}

void StatusBarView::on_can_bitrate_changed(const QString& bitrate) {
    can_bitrate_label_->setText("CAN: " + bitrate);
}

void StatusBarView::on_clock_time_changed(const QString& time) {
    clock_label_->setText(time);
}

}  // namespace CANoeLite::Views
