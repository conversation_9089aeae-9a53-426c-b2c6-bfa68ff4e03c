# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = C:\msys64\ucrt64\bin\cmake.exe

# The command to remove a file.
RM = C:\msys64\ucrt64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\work\01.LAB_ADAS_NW_Tool\CANoeLite

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	C:\msys64\ucrt64\bin\cmake.exe -E echo "No interactive CMake dialog available."
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	C:\msys64\ucrt64\bin\cmake.exe --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(CMAKE_COMMAND) -E cmake_progress_start C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build\CMakeFiles C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build\src\\CMakeFiles\progress.marks
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 src/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 src/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 src/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 src/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
src/CMakeFiles/CANoeLite.dir/rule:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 src/CMakeFiles/CANoeLite.dir/rule
.PHONY : src/CMakeFiles/CANoeLite.dir/rule

# Convenience name for target.
CANoeLite: src/CMakeFiles/CANoeLite.dir/rule
.PHONY : CANoeLite

# fast build rule for target.
CANoeLite/fast:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/build
.PHONY : CANoeLite/fast

# Convenience name for target.
src/CMakeFiles/CANoeLite_autogen_timestamp_deps.dir/rule:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 src/CMakeFiles/CANoeLite_autogen_timestamp_deps.dir/rule
.PHONY : src/CMakeFiles/CANoeLite_autogen_timestamp_deps.dir/rule

# Convenience name for target.
CANoeLite_autogen_timestamp_deps: src/CMakeFiles/CANoeLite_autogen_timestamp_deps.dir/rule
.PHONY : CANoeLite_autogen_timestamp_deps

# fast build rule for target.
CANoeLite_autogen_timestamp_deps/fast:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite_autogen_timestamp_deps.dir\build.make src/CMakeFiles/CANoeLite_autogen_timestamp_deps.dir/build
.PHONY : CANoeLite_autogen_timestamp_deps/fast

# Convenience name for target.
src/CMakeFiles/CANoeLite_autogen.dir/rule:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 src/CMakeFiles/CANoeLite_autogen.dir/rule
.PHONY : src/CMakeFiles/CANoeLite_autogen.dir/rule

# Convenience name for target.
CANoeLite_autogen: src/CMakeFiles/CANoeLite_autogen.dir/rule
.PHONY : CANoeLite_autogen

# fast build rule for target.
CANoeLite_autogen/fast:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite_autogen.dir\build.make src/CMakeFiles/CANoeLite_autogen.dir/build
.PHONY : CANoeLite_autogen/fast

CANoeLite_autogen/3YJK5W5UP7/qrc_resources.obj: CANoeLite_autogen/3YJK5W5UP7/qrc_resources.cpp.obj
.PHONY : CANoeLite_autogen/3YJK5W5UP7/qrc_resources.obj

# target to build an object file
CANoeLite_autogen/3YJK5W5UP7/qrc_resources.cpp.obj:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/CANoeLite_autogen/3YJK5W5UP7/qrc_resources.cpp.obj
.PHONY : CANoeLite_autogen/3YJK5W5UP7/qrc_resources.cpp.obj

CANoeLite_autogen/3YJK5W5UP7/qrc_resources.i: CANoeLite_autogen/3YJK5W5UP7/qrc_resources.cpp.i
.PHONY : CANoeLite_autogen/3YJK5W5UP7/qrc_resources.i

# target to preprocess a source file
CANoeLite_autogen/3YJK5W5UP7/qrc_resources.cpp.i:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/CANoeLite_autogen/3YJK5W5UP7/qrc_resources.cpp.i
.PHONY : CANoeLite_autogen/3YJK5W5UP7/qrc_resources.cpp.i

CANoeLite_autogen/3YJK5W5UP7/qrc_resources.s: CANoeLite_autogen/3YJK5W5UP7/qrc_resources.cpp.s
.PHONY : CANoeLite_autogen/3YJK5W5UP7/qrc_resources.s

# target to generate assembly for a file
CANoeLite_autogen/3YJK5W5UP7/qrc_resources.cpp.s:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/CANoeLite_autogen/3YJK5W5UP7/qrc_resources.cpp.s
.PHONY : CANoeLite_autogen/3YJK5W5UP7/qrc_resources.cpp.s

CANoeLite_autogen/mocs_compilation.obj: CANoeLite_autogen/mocs_compilation.cpp.obj
.PHONY : CANoeLite_autogen/mocs_compilation.obj

# target to build an object file
CANoeLite_autogen/mocs_compilation.cpp.obj:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/CANoeLite_autogen/mocs_compilation.cpp.obj
.PHONY : CANoeLite_autogen/mocs_compilation.cpp.obj

CANoeLite_autogen/mocs_compilation.i: CANoeLite_autogen/mocs_compilation.cpp.i
.PHONY : CANoeLite_autogen/mocs_compilation.i

# target to preprocess a source file
CANoeLite_autogen/mocs_compilation.cpp.i:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/CANoeLite_autogen/mocs_compilation.cpp.i
.PHONY : CANoeLite_autogen/mocs_compilation.cpp.i

CANoeLite_autogen/mocs_compilation.s: CANoeLite_autogen/mocs_compilation.cpp.s
.PHONY : CANoeLite_autogen/mocs_compilation.s

# target to generate assembly for a file
CANoeLite_autogen/mocs_compilation.cpp.s:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/CANoeLite_autogen/mocs_compilation.cpp.s
.PHONY : CANoeLite_autogen/mocs_compilation.cpp.s

core/application.obj: core/application.cpp.obj
.PHONY : core/application.obj

# target to build an object file
core/application.cpp.obj:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/core/application.cpp.obj
.PHONY : core/application.cpp.obj

core/application.i: core/application.cpp.i
.PHONY : core/application.i

# target to preprocess a source file
core/application.cpp.i:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/core/application.cpp.i
.PHONY : core/application.cpp.i

core/application.s: core/application.cpp.s
.PHONY : core/application.s

# target to generate assembly for a file
core/application.cpp.s:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/core/application.cpp.s
.PHONY : core/application.cpp.s

main.obj: main.cpp.obj
.PHONY : main.obj

# target to build an object file
main.cpp.obj:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/main.cpp.obj
.PHONY : main.cpp.obj

main.i: main.cpp.i
.PHONY : main.i

# target to preprocess a source file
main.cpp.i:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/main.cpp.i
.PHONY : main.cpp.i

main.s: main.cpp.s
.PHONY : main.s

# target to generate assembly for a file
main.cpp.s:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/main.cpp.s
.PHONY : main.cpp.s

services/dock_manager.obj: services/dock_manager.cpp.obj
.PHONY : services/dock_manager.obj

# target to build an object file
services/dock_manager.cpp.obj:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/services/dock_manager.cpp.obj
.PHONY : services/dock_manager.cpp.obj

services/dock_manager.i: services/dock_manager.cpp.i
.PHONY : services/dock_manager.i

# target to preprocess a source file
services/dock_manager.cpp.i:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/services/dock_manager.cpp.i
.PHONY : services/dock_manager.cpp.i

services/dock_manager.s: services/dock_manager.cpp.s
.PHONY : services/dock_manager.s

# target to generate assembly for a file
services/dock_manager.cpp.s:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/services/dock_manager.cpp.s
.PHONY : services/dock_manager.cpp.s

services/ribbon_config_service.obj: services/ribbon_config_service.cpp.obj
.PHONY : services/ribbon_config_service.obj

# target to build an object file
services/ribbon_config_service.cpp.obj:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/services/ribbon_config_service.cpp.obj
.PHONY : services/ribbon_config_service.cpp.obj

services/ribbon_config_service.i: services/ribbon_config_service.cpp.i
.PHONY : services/ribbon_config_service.i

# target to preprocess a source file
services/ribbon_config_service.cpp.i:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/services/ribbon_config_service.cpp.i
.PHONY : services/ribbon_config_service.cpp.i

services/ribbon_config_service.s: services/ribbon_config_service.cpp.s
.PHONY : services/ribbon_config_service.s

# target to generate assembly for a file
services/ribbon_config_service.cpp.s:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/services/ribbon_config_service.cpp.s
.PHONY : services/ribbon_config_service.cpp.s

services/settings_service.obj: services/settings_service.cpp.obj
.PHONY : services/settings_service.obj

# target to build an object file
services/settings_service.cpp.obj:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/services/settings_service.cpp.obj
.PHONY : services/settings_service.cpp.obj

services/settings_service.i: services/settings_service.cpp.i
.PHONY : services/settings_service.i

# target to preprocess a source file
services/settings_service.cpp.i:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/services/settings_service.cpp.i
.PHONY : services/settings_service.cpp.i

services/settings_service.s: services/settings_service.cpp.s
.PHONY : services/settings_service.s

# target to generate assembly for a file
services/settings_service.cpp.s:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/services/settings_service.cpp.s
.PHONY : services/settings_service.cpp.s

view_models/dock_widget_view_model.obj: view_models/dock_widget_view_model.cpp.obj
.PHONY : view_models/dock_widget_view_model.obj

# target to build an object file
view_models/dock_widget_view_model.cpp.obj:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/view_models/dock_widget_view_model.cpp.obj
.PHONY : view_models/dock_widget_view_model.cpp.obj

view_models/dock_widget_view_model.i: view_models/dock_widget_view_model.cpp.i
.PHONY : view_models/dock_widget_view_model.i

# target to preprocess a source file
view_models/dock_widget_view_model.cpp.i:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/view_models/dock_widget_view_model.cpp.i
.PHONY : view_models/dock_widget_view_model.cpp.i

view_models/dock_widget_view_model.s: view_models/dock_widget_view_model.cpp.s
.PHONY : view_models/dock_widget_view_model.s

# target to generate assembly for a file
view_models/dock_widget_view_model.cpp.s:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/view_models/dock_widget_view_model.cpp.s
.PHONY : view_models/dock_widget_view_model.cpp.s

view_models/main_window_view_model.obj: view_models/main_window_view_model.cpp.obj
.PHONY : view_models/main_window_view_model.obj

# target to build an object file
view_models/main_window_view_model.cpp.obj:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/view_models/main_window_view_model.cpp.obj
.PHONY : view_models/main_window_view_model.cpp.obj

view_models/main_window_view_model.i: view_models/main_window_view_model.cpp.i
.PHONY : view_models/main_window_view_model.i

# target to preprocess a source file
view_models/main_window_view_model.cpp.i:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/view_models/main_window_view_model.cpp.i
.PHONY : view_models/main_window_view_model.cpp.i

view_models/main_window_view_model.s: view_models/main_window_view_model.cpp.s
.PHONY : view_models/main_window_view_model.s

# target to generate assembly for a file
view_models/main_window_view_model.cpp.s:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/view_models/main_window_view_model.cpp.s
.PHONY : view_models/main_window_view_model.cpp.s

view_models/ribbon_view_model.obj: view_models/ribbon_view_model.cpp.obj
.PHONY : view_models/ribbon_view_model.obj

# target to build an object file
view_models/ribbon_view_model.cpp.obj:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/view_models/ribbon_view_model.cpp.obj
.PHONY : view_models/ribbon_view_model.cpp.obj

view_models/ribbon_view_model.i: view_models/ribbon_view_model.cpp.i
.PHONY : view_models/ribbon_view_model.i

# target to preprocess a source file
view_models/ribbon_view_model.cpp.i:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/view_models/ribbon_view_model.cpp.i
.PHONY : view_models/ribbon_view_model.cpp.i

view_models/ribbon_view_model.s: view_models/ribbon_view_model.cpp.s
.PHONY : view_models/ribbon_view_model.s

# target to generate assembly for a file
view_models/ribbon_view_model.cpp.s:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/view_models/ribbon_view_model.cpp.s
.PHONY : view_models/ribbon_view_model.cpp.s

views/desktop_tabs_widget.obj: views/desktop_tabs_widget.cpp.obj
.PHONY : views/desktop_tabs_widget.obj

# target to build an object file
views/desktop_tabs_widget.cpp.obj:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/views/desktop_tabs_widget.cpp.obj
.PHONY : views/desktop_tabs_widget.cpp.obj

views/desktop_tabs_widget.i: views/desktop_tabs_widget.cpp.i
.PHONY : views/desktop_tabs_widget.i

# target to preprocess a source file
views/desktop_tabs_widget.cpp.i:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/views/desktop_tabs_widget.cpp.i
.PHONY : views/desktop_tabs_widget.cpp.i

views/desktop_tabs_widget.s: views/desktop_tabs_widget.cpp.s
.PHONY : views/desktop_tabs_widget.s

# target to generate assembly for a file
views/desktop_tabs_widget.cpp.s:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/views/desktop_tabs_widget.cpp.s
.PHONY : views/desktop_tabs_widget.cpp.s

views/main_window.obj: views/main_window.cpp.obj
.PHONY : views/main_window.obj

# target to build an object file
views/main_window.cpp.obj:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/views/main_window.cpp.obj
.PHONY : views/main_window.cpp.obj

views/main_window.i: views/main_window.cpp.i
.PHONY : views/main_window.i

# target to preprocess a source file
views/main_window.cpp.i:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/views/main_window.cpp.i
.PHONY : views/main_window.cpp.i

views/main_window.s: views/main_window.cpp.s
.PHONY : views/main_window.s

# target to generate assembly for a file
views/main_window.cpp.s:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/views/main_window.cpp.s
.PHONY : views/main_window.cpp.s

views/ribbon_view.obj: views/ribbon_view.cpp.obj
.PHONY : views/ribbon_view.obj

# target to build an object file
views/ribbon_view.cpp.obj:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/views/ribbon_view.cpp.obj
.PHONY : views/ribbon_view.cpp.obj

views/ribbon_view.i: views/ribbon_view.cpp.i
.PHONY : views/ribbon_view.i

# target to preprocess a source file
views/ribbon_view.cpp.i:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/views/ribbon_view.cpp.i
.PHONY : views/ribbon_view.cpp.i

views/ribbon_view.s: views/ribbon_view.cpp.s
.PHONY : views/ribbon_view.s

# target to generate assembly for a file
views/ribbon_view.cpp.s:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/views/ribbon_view.cpp.s
.PHONY : views/ribbon_view.cpp.s

views/status_bar_view.obj: views/status_bar_view.cpp.obj
.PHONY : views/status_bar_view.obj

# target to build an object file
views/status_bar_view.cpp.obj:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/views/status_bar_view.cpp.obj
.PHONY : views/status_bar_view.cpp.obj

views/status_bar_view.i: views/status_bar_view.cpp.i
.PHONY : views/status_bar_view.i

# target to preprocess a source file
views/status_bar_view.cpp.i:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/views/status_bar_view.cpp.i
.PHONY : views/status_bar_view.cpp.i

views/status_bar_view.s: views/status_bar_view.cpp.s
.PHONY : views/status_bar_view.s

# target to generate assembly for a file
views/status_bar_view.cpp.s:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/views/status_bar_view.cpp.s
.PHONY : views/status_bar_view.cpp.s

views/trace_dock_widget.obj: views/trace_dock_widget.cpp.obj
.PHONY : views/trace_dock_widget.obj

# target to build an object file
views/trace_dock_widget.cpp.obj:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/views/trace_dock_widget.cpp.obj
.PHONY : views/trace_dock_widget.cpp.obj

views/trace_dock_widget.i: views/trace_dock_widget.cpp.i
.PHONY : views/trace_dock_widget.i

# target to preprocess a source file
views/trace_dock_widget.cpp.i:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/views/trace_dock_widget.cpp.i
.PHONY : views/trace_dock_widget.cpp.i

views/trace_dock_widget.s: views/trace_dock_widget.cpp.s
.PHONY : views/trace_dock_widget.s

# target to generate assembly for a file
views/trace_dock_widget.cpp.s:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/views/trace_dock_widget.cpp.s
.PHONY : views/trace_dock_widget.cpp.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... edit_cache
	@echo ... rebuild_cache
	@echo ... CANoeLite_autogen
	@echo ... CANoeLite_autogen_timestamp_deps
	@echo ... CANoeLite
	@echo ... CANoeLite_autogen/3YJK5W5UP7/qrc_resources.obj
	@echo ... CANoeLite_autogen/3YJK5W5UP7/qrc_resources.i
	@echo ... CANoeLite_autogen/3YJK5W5UP7/qrc_resources.s
	@echo ... CANoeLite_autogen/mocs_compilation.obj
	@echo ... CANoeLite_autogen/mocs_compilation.i
	@echo ... CANoeLite_autogen/mocs_compilation.s
	@echo ... core/application.obj
	@echo ... core/application.i
	@echo ... core/application.s
	@echo ... main.obj
	@echo ... main.i
	@echo ... main.s
	@echo ... services/dock_manager.obj
	@echo ... services/dock_manager.i
	@echo ... services/dock_manager.s
	@echo ... services/ribbon_config_service.obj
	@echo ... services/ribbon_config_service.i
	@echo ... services/ribbon_config_service.s
	@echo ... services/settings_service.obj
	@echo ... services/settings_service.i
	@echo ... services/settings_service.s
	@echo ... view_models/dock_widget_view_model.obj
	@echo ... view_models/dock_widget_view_model.i
	@echo ... view_models/dock_widget_view_model.s
	@echo ... view_models/main_window_view_model.obj
	@echo ... view_models/main_window_view_model.i
	@echo ... view_models/main_window_view_model.s
	@echo ... view_models/ribbon_view_model.obj
	@echo ... view_models/ribbon_view_model.i
	@echo ... view_models/ribbon_view_model.s
	@echo ... views/desktop_tabs_widget.obj
	@echo ... views/desktop_tabs_widget.i
	@echo ... views/desktop_tabs_widget.s
	@echo ... views/main_window.obj
	@echo ... views/main_window.i
	@echo ... views/main_window.s
	@echo ... views/ribbon_view.obj
	@echo ... views/ribbon_view.i
	@echo ... views/ribbon_view.s
	@echo ... views/status_bar_view.obj
	@echo ... views/status_bar_view.i
	@echo ... views/status_bar_view.s
	@echo ... views/trace_dock_widget.obj
	@echo ... views/trace_dock_widget.i
	@echo ... views/trace_dock_widget.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

