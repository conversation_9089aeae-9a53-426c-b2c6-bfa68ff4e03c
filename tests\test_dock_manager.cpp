#include <catch2/catch_test_macros.hpp>

#include <QMainWindow>
#include <QWidget>

#include "services/dock_manager.h"
#include "services/settings_service.h"

using namespace CANoeLite::Services;

TEST_CASE("DockManager", "[services]") {
    auto settings_service = std::make_shared<SettingsService>();
    DockManager dock_manager(settings_service);
    
    QMainWindow main_window;
    dock_manager.set_main_window(&main_window);

    SECTION("Create and register dock widget") {
        auto* test_widget = new QWidget();
        test_widget->setWindowTitle("Test Widget");
        
        dock_manager.register_dock_widget("test_dock", test_widget);
        
        // Verify dock widget was created
        REQUIRE(main_window.findChild<QDockWidget*>("test_dock") != nullptr);
    }

    SECTION("Show and hide dock widget") {
        auto* test_widget = new QWidget();
        test_widget->setWindowTitle("Test Widget");
        
        dock_manager.register_dock_widget("test_dock", test_widget);
        
        // Test show/hide
        dock_manager.hide_dock_widget("test_dock");
        auto* dock_widget = main_window.findChild<QDockWidget*>("test_dock");
        REQUIRE(dock_widget != nullptr);
        REQUIRE(!dock_widget->isVisible());
        
        dock_manager.show_dock_widget("test_dock");
        REQUIRE(dock_widget->isVisible());
    }
}
