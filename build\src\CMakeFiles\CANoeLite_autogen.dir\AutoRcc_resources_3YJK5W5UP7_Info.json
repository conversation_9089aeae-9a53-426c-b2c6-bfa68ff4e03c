{"BUILD_DIR": "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/build/src/CANoeLite_autogen", "CMAKE_BINARY_DIR": "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/build", "CMAKE_CURRENT_BINARY_DIR": "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/build/src", "CMAKE_CURRENT_SOURCE_DIR": "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src", "CMAKE_SOURCE_DIR": "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite", "CROSS_CONFIG": false, "GENERATOR": "MinGW Makefiles", "INCLUDE_DIR": "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/build/src/CANoeLite_autogen/include", "INPUTS": ["C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/resources/ribbon.json"], "LOCK_FILE": "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/build/src/CMakeFiles/CANoeLite_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Lock.lock", "MULTI_CONFIG": false, "OPTIONS": ["-name", "resources"], "OUTPUT_CHECKSUM": "3YJK5W5UP7", "OUTPUT_NAME": "qrc_resources.cpp", "RCC_EXECUTABLE": "C:/msys64/ucrt64/share/qt6/bin/rcc.exe", "RCC_LIST_OPTIONS": ["--list"], "SETTINGS_FILE": "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/build/src/CMakeFiles/CANoeLite_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Used.txt", "SOURCE": "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/resources/resources.qrc", "USE_BETTER_GRAPH": true, "VERBOSITY": 0}