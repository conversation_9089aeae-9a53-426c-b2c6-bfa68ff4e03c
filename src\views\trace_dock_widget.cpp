#include "views/trace_dock_widget.h"

#include <QHeaderView>
#include <QHBoxLayout>
#include <QPushButton>
#include <QVBoxLayout>

#include "view_models/dock_widget_view_model.h"

namespace CANoeLite::Views {

TraceDockWidget::TraceDockWidget(QWidget* parent) : QWidget(parent) {
    view_model_ = std::make_shared<ViewModels::DockWidgetViewModel>("Trace View", this);
    setWindowTitle(view_model_->title());
    setup_ui();
    setup_sample_data();
}

void TraceDockWidget::setup_ui() {
    auto* layout = new QVBoxLayout(this);

    // Toolbar
    auto* toolbar_layout = new QHBoxLayout();
    auto* add_data_button = new QPushButton("Add Sample Data");
    auto* clear_button = new QPushButton("Clear");

    connect(add_data_button, &QPushButton::clicked, this, &TraceDockWidget::add_sample_data);
    connect(clear_button, &QPushButton::clicked, this, &TraceDockWidget::clear_trace);

    toolbar_layout->addWidget(add_data_button);
    toolbar_layout->addWidget(clear_button);
    toolbar_layout->addStretch();

    layout->addLayout(toolbar_layout);

    // Trace table
    trace_table_ = new QTableWidget(this);
    trace_table_->setColumnCount(5);
    QStringList headers = {"Time", "ID", "Type", "Length", "Data"};
    trace_table_->setHorizontalHeaderLabels(headers);

    // Configure table
    trace_table_->horizontalHeader()->setStretchLastSection(true);
    trace_table_->setAlternatingRowColors(true);
    trace_table_->setSelectionBehavior(QAbstractItemView::SelectRows);

    layout->addWidget(trace_table_);
}

void TraceDockWidget::setup_sample_data() {
    add_sample_data();
}

void TraceDockWidget::add_sample_data() {
    int row = trace_table_->rowCount();
    trace_table_->insertRow(row);

    // Sample CAN message data
    trace_table_->setItem(row, 0, new QTableWidgetItem(QString("00:00:%1.%2").arg(row / 10).arg(row % 10, 3, 10, QChar('0'))));
    trace_table_->setItem(row, 1, new QTableWidgetItem(QString("0x%1").arg(0x100 + row, 3, 16, QChar('0')).toUpper()));
    trace_table_->setItem(row, 2, new QTableWidgetItem("CAN"));
    trace_table_->setItem(row, 3, new QTableWidgetItem("8"));
    trace_table_->setItem(row, 4, new QTableWidgetItem("01 02 03 04 05 06 07 08"));

    // Scroll to bottom
    trace_table_->scrollToBottom();
}

void TraceDockWidget::clear_trace() {
    trace_table_->setRowCount(0);
}

}  // namespace CANoeLite::Views
