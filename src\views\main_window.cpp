#include "views/main_window.h"

#include <QCloseEvent>
#include <QVBoxLayout>
#include <QWidget>

#include "core/application.h"
#include "view_models/main_window_view_model.h"
#include "views/desktop_tabs_widget.h"
#include "views/ribbon_view.h"
#include "views/status_bar_view.h"

namespace CANoeLite::Views {

MainWindow::MainWindow(std::shared_ptr<ViewModels::MainWindowViewModel> view_model, QWidget* parent)
    : QMainWindow(parent), view_model_(view_model) {
    setup_ui();
    setup_connections();
}

void MainWindow::setup_ui() {
    setWindowTitle(view_model_->window_title());
    setMinimumSize(1200, 800);

    // Create central widget
    auto* central_widget = new QWidget(this);
    setCentralWidget(central_widget);

    auto* layout = new QVBoxLayout(central_widget);
    layout->setContentsMargins(0, 0, 0, 0);
    layout->setSpacing(0);

    // Create ribbon
    ribbon_view_ = new RibbonView(view_model_->ribbon_view_model(), this);
    layout->addWidget(ribbon_view_);

    // Create desktop tabs
    desktop_tabs_ = new DesktopTabsWidget(this);
    layout->addWidget(desktop_tabs_);

    // Add stretch to fill remaining space
    layout->addStretch();

    // Create status bar
    status_bar_view_ = new StatusBarView(view_model_, this);
    setStatusBar(status_bar_view_);

    // Set dock options
    setDockOptions(QMainWindow::AnimatedDocks | QMainWindow::AllowNestedDocks | QMainWindow::AllowTabbedDocks);
}

void MainWindow::setup_connections() {
    connect(view_model_.get(), &ViewModels::MainWindowViewModel::window_title_changed, this,
            &MainWindow::on_window_title_changed);
}

void MainWindow::closeEvent(QCloseEvent* event) {
    // Save layout before closing
    if (auto app = Core::Application::instance()) {
        app->dock_manager()->save_layout();
    }
    event->accept();
}

void MainWindow::on_window_title_changed(const QString& title) {
    setWindowTitle(title);
}

}  // namespace CANoeLite::Views
