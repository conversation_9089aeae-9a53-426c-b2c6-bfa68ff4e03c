#pragma once

#include <QTabWidget>
#include <QWidget>

namespace CANoeLite::Views {

/**
 * @brief Desktop tabs widget for managing window areas
 */
class DesktopTabsWidget : public QTabWidget {
    Q_OBJECT

public:
    explicit DesktopTabsWidget(QWidget* parent = nullptr);
    ~DesktopTabsWidget() override = default;

    /**
     * @brief Add a new window area
     */
    void add_window_area(const QString& name);

    /**
     * @brief Remove a window area
     */
    void remove_window_area(int index);

private slots:
    void on_tab_close_requested(int index);
    void on_add_tab_clicked();

private:
    void setup_ui();
    void create_default_area();

    int window_area_counter_;
};

}  // namespace CANoeLite::Views
