{"AUTOGEN_COMMAND_LINE_LENGTH_MAX": 32000, "BUILD_DIR": "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/build/src/CANoeLite_autogen", "CMAKE_BINARY_DIR": "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/build", "CMAKE_CURRENT_BINARY_DIR": "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/build/src", "CMAKE_CURRENT_SOURCE_DIR": "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src", "CMAKE_EXECUTABLE": "C:/msys64/ucrt64/bin/cmake.exe", "CMAKE_LIST_FILES": ["C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/CMakeLists.txt", "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/resources/resources.qrc", "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/qml/qml.qrc"], "CMAKE_SOURCE_DIR": "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite", "CROSS_CONFIG": false, "DEP_FILE": "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/build/src/CANoeLite_autogen/deps", "DEP_FILE_RULE_NAME": "CANoeLite_autogen/timestamp", "HEADERS": [["C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/core/application.h", "MU", "TAC5DWH4SE/moc_application.cpp", null], ["C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/core/interfaces.h", "MU", "TAC5DWH4SE/moc_interfaces.cpp", null], ["C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/services/dock_manager.h", "MU", "3BYFHCGL5U/moc_dock_manager.cpp", null], ["C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/services/ribbon_config_service.h", "MU", "3BYFHCGL5U/moc_ribbon_config_service.cpp", null], ["C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/services/settings_service.h", "MU", "3BYFHCGL5U/moc_settings_service.cpp", null], ["C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/view_models/dock_widget_view_model.h", "MU", "NGVWNOU7JT/moc_dock_widget_view_model.cpp", null], ["C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/view_models/main_window_view_model.h", "MU", "NGVWNOU7JT/moc_main_window_view_model.cpp", null], ["C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/view_models/ribbon_view_model.h", "MU", "NGVWNOU7JT/moc_ribbon_view_model.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/build/src/CANoeLite_autogen/include", "MOC_COMPILATION_FILE": "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/build/src/CANoeLite_autogen/mocs_compilation.cpp", "MOC_DEFINITIONS": ["MINGW_HAS_SECURE_API=1", "QT_CORE_LIB", "QT_GUI_LIB", "QT_NETWORK_LIB", "QT_NO_DEBUG", "QT_OPENGL_LIB", "QT_QMLINTEGRATION_LIB", "QT_QMLMETA_LIB", "QT_QMLMODELS_LIB", "QT_QMLWORKERSCRIPT_LIB", "QT_QML_LIB", "QT_QUICKCONTROLS2_LIB", "QT_QUICK_LIB", "QT_WIDGETS_LIB", "UNICODE", "WIN32", "WIN64", "WINVER=0x0A00", "_ENABLE_EXTENDED_ALIGNED_STORAGE", "_UNICODE", "_WIN32_WINNT=0x0A00", "_WIN64"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": ["C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src", "C:/msys64/ucrt64/include/qt6/QtCore", "C:/msys64/ucrt64/include/qt6", "C:/msys64/ucrt64/share/qt6/mkspecs/win32-g++", "C:/msys64/ucrt64/include/qt6/QtWidgets", "C:/msys64/ucrt64/include/qt6/QtGui", "C:/msys64/ucrt64/include/qt6/QtQuick", "C:/msys64/ucrt64/include/qt6/QtQml", "C:/msys64/ucrt64/include/qt6/QtQmlIntegration", "C:/msys64/ucrt64/include/qt6/QtNetwork", "C:/msys64/ucrt64/include/qt6/QtQmlMeta", "C:/msys64/ucrt64/include/qt6/QtQmlModels", "C:/msys64/ucrt64/include/qt6/QtQmlWorkerScript", "C:/msys64/ucrt64/include/qt6/QtOpenGL", "C:/msys64/ucrt64/include/qt6/QtQuickControls2", "C:/msys64/ucrt64/include", "C:/msys64/ucrt64/include/c++/15.1.0", "C:/msys64/ucrt64/include/c++/15.1.0/x86_64-w64-mingw32", "C:/msys64/ucrt64/include/c++/15.1.0/backward", "C:/msys64/ucrt64/lib/gcc/x86_64-w64-mingw32/15.1.0/include", "C:/msys64/ucrt64/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT", "Q_GADGET_EXPORT", "Q_ENUM_NS"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": ["C:/msys64/ucrt64/bin/c++.exe", "-std=gnu++20", "-w", "-dM", "-E", "C:/msys64/ucrt64/share/cmake/Modules/CMakeCXXCompilerABI.cpp"], "MOC_PREDEFS_FILE": "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/build/src/CANoeLite_autogen/moc_predefs.h", "MOC_RELAXED_MODE": false, "MOC_SKIP": [], "MULTI_CONFIG": false, "PARALLEL": 4, "PARSE_CACHE_FILE": "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/build/src/CMakeFiles/CANoeLite_autogen.dir/ParseCache.txt", "QT_MOC_EXECUTABLE": "C:/msys64/ucrt64/share/qt6/bin/moc.exe", "QT_UIC_EXECUTABLE": "C:/msys64/ucrt64/share/qt6/bin/uic.exe", "QT_VERSION_MAJOR": 6, "QT_VERSION_MINOR": 9, "SETTINGS_FILE": "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/build/src/CMakeFiles/CANoeLite_autogen.dir/AutogenUsed.txt", "SOURCES": [["C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/core/application.cpp", "MU", null], ["C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/main.cpp", "MU", null], ["C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/services/dock_manager.cpp", "MU", null], ["C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/services/ribbon_config_service.cpp", "MU", null], ["C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/services/settings_service.cpp", "MU", null], ["C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/view_models/dock_widget_view_model.cpp", "MU", null], ["C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/view_models/main_window_view_model.cpp", "MU", null], ["C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/view_models/ribbon_view_model.cpp", "MU", null]], "UIC_OPTIONS": [], "UIC_SEARCH_PATHS": [], "UIC_SKIP": [], "UIC_UI_FILES": [], "USE_BETTER_GRAPH": true, "VERBOSITY": 0}