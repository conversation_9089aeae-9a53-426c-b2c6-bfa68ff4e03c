import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../theme"
import "../components"

Rectangle {
    id: root
    
    property var viewModel: null
    property bool collapsed: false
    
    color: Colors.ribbonBackground
    border.color: Colors.border
    border.width: AppTheme.border.widthThin
    
    function initialize() {
        if (viewModel) {
            tabBar.currentIndex = 0
            loadRibbonConfiguration()
        }
    }
    
    function loadRibbonConfiguration() {
        // Load ribbon configuration from ViewModel
        if (viewModel && viewModel.ribbonData) {
            tabRepeater.model = viewModel.ribbonTabs
        }
    }
    
    ColumnLayout {
        anchors.fill: parent
        spacing: 0
        
        // Tab Bar
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: AppTheme.ribbon.tabHeight
            color: Colors.ribbonBackground
            
            TabBar {
                id: tabBar
                anchors.fill: parent
                background: Rectangle {
                    color: "transparent"
                }
                
                Repeater {
                    id: tabRepeater
                    model: viewModel ? viewModel.ribbonTabs : []
                    
                    TabButton {
                        text: modelData.name || ""
                        
                        background: Rectangle {
                            color: parent.checked ? Colors.ribbonTabActive : 
                                   parent.hovered ? Colors.ribbonTabHover : Colors.ribbonTabInactive
                            radius: AppTheme.radius.sm
                            
                            Behavior on color {
                                ColorAnimation {
                                    duration: AppTheme.animations.ribbon.buttonHoverDuration
                                    easing.type: AppTheme.animations.ribbon.buttonHoverEasing
                                }
                            }
                        }
                        
                        contentItem: Text {
                            text: parent.text
                            font.family: AppTheme.typography.ribbon.fontFamily
                            font.pixelSize: AppTheme.typography.ribbon.tabFontSize
                            font.weight: AppTheme.typography.ribbon.tabFontWeight
                            color: parent.checked ? Colors.onPrimary : Colors.onSecondary
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                            
                            Behavior on color {
                                ColorAnimation {
                                    duration: AppTheme.animations.ribbon.buttonHoverDuration
                                    easing.type: AppTheme.animations.ribbon.buttonHoverEasing
                                }
                            }
                        }
                    }
                }
            }
        }
        
        // Ribbon Content
        Rectangle {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: Colors.ribbonPanelBackground
            visible: !collapsed
            
            StackLayout {
                id: stackLayout
                anchors.fill: parent
                anchors.margins: AppTheme.spacing.sm
                currentIndex: tabBar.currentIndex
                
                Repeater {
                    model: viewModel ? viewModel.ribbonTabs : []
                    
                    ScrollView {
                        clip: true
                        
                        RowLayout {
                            spacing: AppTheme.spacing.md
                            height: parent.height
                            
                            Repeater {
                                model: modelData.panels || []
                                
                                RibbonPanel {
                                    title: modelData.name || ""
                                    buttons: modelData.buttons || []
                                    onButtonClicked: function(action) {
                                        if (root.viewModel) {
                                            root.viewModel.executeCommand(action)
                                        }
                                    }
                                }
                            }
                            
                            Item { Layout.fillWidth: true } // Spacer
                        }
                    }
                }
            }
        }
        
        // Collapse/Expand Animation
        Behavior on Layout.preferredHeight {
            NumberAnimation {
                duration: AppTheme.animations.ribbon.collapseDuration
                easing.type: AppTheme.animations.ribbon.collapseEasing
            }
        }
    }
    
    // Collapse/Expand functionality
    MouseArea {
        anchors.fill: parent
        acceptedButtons: Qt.RightButton
        onClicked: {
            if (mouse.button === Qt.RightButton) {
                contextMenu.popup()
            }
        }
        
        onDoubleClicked: {
            collapsed = !collapsed
        }
    }
    
    // Context Menu
    Menu {
        id: contextMenu
        
        MenuItem {
            text: collapsed ? "Expand Ribbon" : "Collapse Ribbon"
            onTriggered: collapsed = !collapsed
        }
        
        MenuSeparator {}
        
        MenuItem {
            text: "Customize Ribbon..."
            onTriggered: {
                if (viewModel) {
                    viewModel.showCustomizeRibbonDialog()
                }
            }
        }
    }
    
    // Connections to ViewModel
    Connections {
        target: viewModel
        
        function onRibbonDataChanged() {
            loadRibbonConfiguration()
        }
        
        function onActiveTabChanged(tabIndex) {
            tabBar.currentIndex = tabIndex
        }
    }
}
