pragma Singleton
import QtQuick 2.15

QtObject {
    // Font Families
    readonly property string primaryFont: "Segoe UI"
    readonly property string secondaryFont: "Arial"
    readonly property string monoFont: "Consolas"
    
    // Font Sizes
    readonly property int displayLarge: 32
    readonly property int displayMedium: 28
    readonly property int displaySmall: 24
    
    readonly property int headlineLarge: 22
    readonly property int headlineMedium: 20
    readonly property int headlineSmall: 18
    
    readonly property int titleLarge: 16
    readonly property int titleMedium: 14
    readonly property int titleSmall: 12
    
    readonly property int bodyLarge: 14
    readonly property int bodyMedium: 12
    readonly property int bodySmall: 11
    
    readonly property int labelLarge: 12
    readonly property int labelMedium: 11
    readonly property int labelSmall: 10
    
    // Font Weights
    readonly property int weightThin: Font.Thin
    readonly property int weightLight: Font.Light
    readonly property int weightNormal: Font.Normal
    readonly property int weightMedium: Font.Medium
    readonly property int weightBold: Font.Bold
    readonly property int weightExtraBold: Font.ExtraBold
    
    // Line Heights (as multipliers)
    readonly property real lineHeightTight: 1.2
    readonly property real lineHeightNormal: 1.4
    readonly property real lineHeightLoose: 1.6
    
    // Letter Spacing
    readonly property real letterSpacingTight: -0.5
    readonly property real letterSpacingNormal: 0
    readonly property real letterSpacingWide: 0.5
    
    // Component-specific Typography
    readonly property QtObject ribbon: QtObject {
        readonly property string fontFamily: primaryFont
        readonly property int tabFontSize: titleMedium
        readonly property int tabFontWeight: weightMedium
        readonly property int buttonFontSize: bodyMedium
        readonly property int buttonFontWeight: weightNormal
        readonly property int panelTitleSize: labelLarge
        readonly property int panelTitleWeight: weightMedium
    }
    
    readonly property QtObject dock: QtObject {
        readonly property string fontFamily: primaryFont
        readonly property int titleFontSize: titleSmall
        readonly property int titleFontWeight: weightMedium
        readonly property int contentFontSize: bodyMedium
        readonly property int contentFontWeight: weightNormal
    }
    
    readonly property QtObject status: QtObject {
        readonly property string fontFamily: primaryFont
        readonly property int textFontSize: bodySmall
        readonly property int textFontWeight: weightNormal
        readonly property int clockFontSize: bodyMedium
        readonly property int clockFontWeight: weightMedium
    }
    
    readonly property QtObject button: QtObject {
        readonly property string fontFamily: primaryFont
        readonly property int fontSize: bodyMedium
        readonly property int fontWeight: weightMedium
        readonly property real letterSpacing: letterSpacingNormal
    }
    
    readonly property QtObject tab: QtObject {
        readonly property string fontFamily: primaryFont
        readonly property int fontSize: titleSmall
        readonly property int fontWeight: weightMedium
        readonly property real letterSpacing: letterSpacingWide
    }
}
