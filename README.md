# CANoeLite

A simplified Vector CANoe-style desktop application built with Qt 6 and C++20, following MVVM architecture principles.

## Features

- **Ribbon Interface**: Collapsible ribbon with tabs (File, Home, Analysis, Simulation, Test, Diagnostics, Environment, Hardware, Tools, Layout)
- **Quick Access Button**: Show/hide ribbon buttons and customize ribbon functionality
- **Desktop Tabs**: Manage multiple window areas like VS Code editor tabs
- **Dock Widgets**: Resizable, floatable, closable nested windows
- **Status Bar**: Application state, progress indicator, CAN bitrate, and clock
- **MVVM Architecture**: Clean separation of concerns with ViewModels, Services, and Views
- **JSON Configuration**: Ribbon layout loaded from JSON configuration
- **Layout Persistence**: Save and restore window layouts using QSettings

## Architecture

```
CANoeLite/
├── src/
│   ├── Core/           # Application core and interfaces
│   ├── ViewModels/     # MVVM ViewModels
│   ├── Views/          # Qt Widgets UI
│   ├── Services/       # Business logic services
│   └── Resources/      # JSON configs and resources
├── tests/              # Unit tests using Catch2
└── 3rdparty/          # Third-party dependencies
```

## Prerequisites

### MSYS2 UCRT64 Environment

1. **Install MSYS2**: Download from [https://www.msys2.org/](https://www.msys2.org/)

2. **Update MSYS2**:
   ```bash
   pacman -Syu
   ```

3. **Install required packages**:
   ```bash
   # Core development tools
   pacman -S mingw-w64-ucrt-x86_64-toolchain
   pacman -S mingw-w64-ucrt-x86_64-cmake
   pacman -S mingw-w64-ucrt-x86_64-ninja
   pacman -S mingw-w64-ucrt-x86_64-pkg-config
   
   # Qt 6 packages
   pacman -S mingw-w64-ucrt-x86_64-qt6-base
   pacman -S mingw-w64-ucrt-x86_64-qt6-tools
   
   # Additional development tools
   pacman -S git
   pacman -S make
   ```

4. **Set up environment**: Add MSYS2 UCRT64 to your PATH:
   ```bash
   export PATH="/ucrt64/bin:$PATH"
   ```

## Building

### 1. Clone and Setup

```bash
git clone <repository-url>
cd CANoeLite
```

### 2. Configure with CMake

```bash
# Create build directory
mkdir build
cd build

# Configure for Debug
cmake -G "Ninja" -DCMAKE_BUILD_TYPE=Debug ..

# Or configure for Release
cmake -G "Ninja" -DCMAKE_BUILD_TYPE=Release ..
```

### 3. Build

```bash
# Build the application
ninja

# Or build specific targets
ninja CANoeLite          # Main application
ninja CANoeLite_Tests     # Unit tests
```

### 4. Run

```bash
# Run the application
./bin/CANoeLite.exe

# Run tests
./bin/CANoeLite_Tests.exe
```

## Development

### Code Style

The project uses clang-format with Google style (120 column limit). Format code with:

```bash
find src -name "*.cpp" -o -name "*.h" | xargs clang-format -i
```

### Testing

Unit tests are built using Catch2. Run tests with:

```bash
cd build
ninja CANoeLite_Tests
./bin/CANoeLite_Tests.exe
```

### Adding New Features

1. **Services**: Add business logic to `src/Services/`
2. **ViewModels**: Add MVVM ViewModels to `src/ViewModels/`
3. **Views**: Add Qt Widgets to `src/Views/`
4. **Tests**: Add unit tests to `tests/`

### Ribbon Configuration

Ribbon layout is configured via `src/resources/ribbon.json`. Example structure:

```json
{
    "tabs": {
        "Analysis": {
            "name": "Analysis",
            "panels": [
                {
                    "name": "Analysis Tools",
                    "buttons": [
                        {
                            "name": "Trace",
                            "icon": "trace",
                            "action": "show_trace_view"
                        }
                    ]
                }
            ]
        }
    }
}
```

## Project Structure

```
CANoeLite/
├── CMakeLists.txt              # Main CMake configuration
├── .clang-format              # Code formatting rules
├── README.md                  # This file
├── cmake/
│   └── CPM.cmake             # CPM dependency manager
├── src/
│   ├── CMakeLists.txt        # Source CMake configuration
│   ├── main.cpp              # Application entry point
│   ├── core/
│   │   ├── application.h/.cpp    # Main application class
│   │   └── interfaces.h          # Core interfaces (MVVM, Command, Observer)
│   ├── view_models/
│   │   ├── main_window_view_model.h/.cpp     # Main window ViewModel
│   │   ├── ribbon_view_model.h/.cpp          # Ribbon ViewModel
│   │   └── dock_widget_view_model.h/.cpp     # Dock widget ViewModel
│   ├── views/
│   │   ├── main_window.h/.cpp                # Main window View
│   │   ├── ribbon_view.h/.cpp                # Ribbon View
│   │   ├── desktop_tabs_widget.h/.cpp        # Desktop tabs View
│   │   ├── status_bar_view.h/.cpp            # Status bar View
│   │   └── trace_dock_widget.h/.cpp          # Sample dock widget
│   ├── services/
│   │   ├── dock_manager.h/.cpp               # Dock widget management
│   │   ├── ribbon_config_service.h/.cpp      # Ribbon configuration
│   │   └── settings_service.h/.cpp           # Settings persistence
│   └── resources/
│       ├── resources.qrc     # Qt resource file
│       └── ribbon.json       # Ribbon configuration
└── tests/
    ├── CMakeLists.txt        # Test CMake configuration
    ├── test_main.cpp         # Test main setup
    ├── test_ribbon_config_service.cpp    # Service tests
    ├── test_dock_manager.cpp             # Dock manager tests
    └── test_view_models.cpp              # ViewModel tests
```

## License

This project is open source. See LICENSE file for details.

## Contributing

1. Follow the established code style (.clang-format)
2. Add unit tests for new functionality
3. Update documentation as needed
4. Follow MVVM and SOLID principles
