#include <catch2/catch_test_macros.hpp>

#include <QSignalSpy>

#include "view_models/dock_widget_view_model.h"
#include "view_models/ribbon_view_model.h"
#include "services/ribbon_config_service.h"
#include "services/settings_service.h"

using namespace CANoeLite::ViewModels;
using namespace CANoeLite::Services;

TEST_CASE("DockWidgetViewModel", "[view_models]") {
    DockWidgetViewModel view_model("Test Title");

    SECTION("Initial state") {
        REQUIRE(view_model.title() == "Test Title");
        REQUIRE(!view_model.is_active());
    }

    SECTION("Property changes emit signals") {
        QSignalSpy title_spy(&view_model, &DockWidgetViewModel::title_changed);
        QSignalSpy active_spy(&view_model, &DockWidgetViewModel::active_changed);

        view_model.set_title("New Title");
        view_model.set_active(true);

        REQUIRE(title_spy.count() == 1);
        REQUIRE(active_spy.count() == 1);
        REQUIRE(view_model.title() == "New Title");
        REQUIRE(view_model.is_active());
    }
}

TEST_CASE("RibbonViewModel", "[view_models]") {
    auto settings_service = std::make_shared<SettingsService>();
    auto ribbon_config_service = std::make_shared<RibbonConfigService>(settings_service);
    RibbonViewModel view_model(ribbon_config_service);

    SECTION("Initial state") {
        REQUIRE(view_model.quick_access_visible());
        REQUIRE(view_model.current_tab() == "File");
    }

    SECTION("Tab changes") {
        QSignalSpy tab_spy(&view_model, &RibbonViewModel::current_tab_changed);
        
        view_model.set_current_tab("Analysis");
        
        REQUIRE(tab_spy.count() == 1);
        REQUIRE(view_model.current_tab() == "Analysis");
    }

    SECTION("Quick access visibility") {
        QSignalSpy visibility_spy(&view_model, &RibbonViewModel::quick_access_visibility_changed);
        
        view_model.set_quick_access_visible(false);
        
        REQUIRE(visibility_spy.count() == 1);
        REQUIRE(!view_model.quick_access_visible());
    }
}
