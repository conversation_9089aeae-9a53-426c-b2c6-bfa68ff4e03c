import QtQuick 2.15
import QtQuick.Controls 2.15
import "../theme"
import "../components"

TabButton {
    id: root
    
    property string tabName: ""
    property bool isActive: false
    
    text: tabName
    
    background: Rectangle {
        color: isActive ? Colors.primary : 
               root.hovered ? Colors.ribbonTabHover : "transparent"
        radius: AppTheme.radius.sm
        
        Behavior on color {
            ColorAnimation {
                duration: AppTheme.animations.hover.duration
                easing.type: AppTheme.animations.hover.easing
            }
        }
    }
    
    contentItem: Text {
        text: root.text
        font.family: AppTheme.typography.ribbon.fontFamily
        font.pixelSize: AppTheme.typography.ribbon.tabFontSize
        font.weight: AppTheme.typography.ribbon.tabFontWeight
        color: isActive ? Colors.onPrimary : Colors.onBackground
        horizontalAlignment: Text.AlignHCenter
        verticalAlignment: Text.AlignVCenter
        
        Behavior on color {
            ColorAnimation {
                duration: AppTheme.animations.hover.duration
                easing.type: AppTheme.animations.hover.easing
            }
        }
    }
}
