#include "core/application.h"

#include <QDir>
#include <QStandardPaths>

#include "services/dock_manager.h"
#include "services/ribbon_config_service.h"
#include "services/settings_service.h"
#include "view_models/main_window_view_model.h"
#include "views/main_window.h"

namespace CANoeLite::Core {

Application* Application::instance_ = nullptr;

Application::Application(int& argc, char** argv) : QApplication(argc, argv), main_window_(nullptr) {
    instance_ = this;
    setApplicationName("CANoeLite");
    setApplicationVersion("1.0.0");
    setOrganizationName("CANoeLite");
    setOrganizationDomain("canoe-lite.local");
}

Application::~Application() {
    delete main_window_;
    instance_ = nullptr;
}

void Application::initialize() {
    setup_services();
    setup_view_models();
    setup_main_window();

    // Load initial configuration
    ribbon_config_service_->load_configuration();
    dock_manager_->restore_layout();

    main_window_->show();
}

Application* Application::instance() {
    return instance_;
}

void Application::setup_services() {
    // Create services
    settings_service_ = std::make_shared<Services::SettingsService>();
    ribbon_config_service_ = std::make_shared<Services::RibbonConfigService>(settings_service_);
    dock_manager_ = std::make_shared<Services::DockManager>(settings_service_);
}

void Application::setup_view_models() {
    // Create view models with service dependencies
    main_window_view_model_ = std::make_shared<ViewModels::MainWindowViewModel>(dock_manager_, ribbon_config_service_);
}

void Application::setup_main_window() {
    main_window_ = new Views::MainWindow(main_window_view_model_);
    dock_manager_->set_main_window(main_window_);
}

}  // namespace CANoeLite::Core
