#include "core/application.h"

#include <QDir>
#include <QStandardPaths>

#include "services/dock_manager.h"
#include "services/ribbon_config_service.h"
#include "services/settings_service.h"
#include "view_models/main_window_view_model.h"

namespace CANoeLite::Core {

Application::Application(QObject* parent) : QObject(parent) {
    // Application metadata is now set in main.cpp
}

Application::~Application() = default;

bool Application::initialize() {
    setup_services();
    setup_view_models();

    // Load initial configuration
    ribbon_config_service_->load_configuration();
    dock_manager_->restore_layout();

    return true;
}

void Application::setup_services() {
    // Create services
    settings_service_ = std::make_shared<Services::SettingsService>();
    ribbon_config_service_ = std::make_shared<Services::RibbonConfigService>(settings_service_);
    dock_manager_ = std::make_shared<Services::DockManager>(settings_service_);
}

void Application::setup_view_models() {
    // Create view models with service dependencies
    main_window_view_model_ = std::make_shared<ViewModels::MainWindowViewModel>(dock_manager_, ribbon_config_service_);
}



}  // namespace CANoeLite::Core
