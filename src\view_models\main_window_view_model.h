#pragma once

#include <QObject>
#include <QString>
#include <memory>

#include "core/interfaces.h"

namespace CANoeLite::Services {
class DockManager;
class RibbonConfigService;
}  // namespace CANoeLite::Services

namespace CANoeLite::ViewModels {

class RibbonViewModel;

/**
 * @brief ViewModel for the main window
 */
class MainWindowViewModel : public Core::IViewModel {
    Q_OBJECT

    // QML Properties
    Q_PROPERTY(QString windowTitle READ window_title WRITE set_window_title NOTIFY window_title_changed)
    Q_PROPERTY(QString statusText READ status_text WRITE set_status_text NOTIFY status_text_changed)
    Q_PROPERTY(QString canBitrate READ can_bitrate WRITE set_can_bitrate NOTIFY can_bitrate_changed)
    Q_PROPERTY(QString clockTime READ clock_time NOTIFY clock_time_changed)
    Q_PROPERTY(QObject* ribbonViewModel READ ribbon_view_model_qml CONSTANT)

public:
    explicit MainWindowViewModel(std::shared_ptr<Services::DockManager> dock_manager,
                                 std::shared_ptr<Services::RibbonConfigService> ribbon_config_service,
                                 QObject* parent = nullptr);
    ~MainWindowViewModel() override = default;

    /**
     * @brief Get the ribbon view model
     */
    std::shared_ptr<RibbonViewModel> ribbon_view_model() const { return ribbon_view_model_; }

    /**
     * @brief Get the ribbon view model for QML (returns QObject*)
     */
    QObject* ribbon_view_model_qml() const;

    /**
     * @brief Commands
     */
    std::shared_ptr<Core::ICommand> show_trace_view_command() const { return show_trace_view_command_; }
    std::shared_ptr<Core::ICommand> show_statistics_command() const { return show_statistics_command_; }
    std::shared_ptr<Core::ICommand> file_new_command() const { return file_new_command_; }
    std::shared_ptr<Core::ICommand> file_open_command() const { return file_open_command_; }
    std::shared_ptr<Core::ICommand> file_save_command() const { return file_save_command_; }

    /**
     * @brief Properties
     */
    QString window_title() const { return window_title_; }
    QString status_text() const { return status_text_; }
    QString can_bitrate() const { return can_bitrate_; }
    QString clock_time() const { return clock_time_; }

public slots:
    void set_window_title(const QString& title);
    void set_status_text(const QString& text);
    void set_can_bitrate(const QString& bitrate);
    void update_clock();

    // QML-specific methods
    Q_INVOKABLE void executeCommand(const QString& action);
    Q_INVOKABLE void showCustomizeRibbonDialog();
    Q_INVOKABLE void saveWindowState();

signals:
    void window_title_changed(const QString& title);
    void status_text_changed(const QString& text);
    void can_bitrate_changed(const QString& bitrate);
    void clock_time_changed(const QString& time);

    // QML-specific signals
    void showMessage(const QString& message, const QString& type);
    void windowTitleChanged(const QString& title);

private:
    void setup_commands();
    void show_trace_view();
    void show_statistics();
    void file_new();
    void file_open();
    void file_save();

    std::shared_ptr<Services::DockManager> dock_manager_;
    std::shared_ptr<Services::RibbonConfigService> ribbon_config_service_;
    std::shared_ptr<RibbonViewModel> ribbon_view_model_;

    // Commands
    std::shared_ptr<Core::ICommand> show_trace_view_command_;
    std::shared_ptr<Core::ICommand> show_statistics_command_;
    std::shared_ptr<Core::ICommand> file_new_command_;
    std::shared_ptr<Core::ICommand> file_open_command_;
    std::shared_ptr<Core::ICommand> file_save_command_;

    // Properties
    QString window_title_;
    QString status_text_;
    QString can_bitrate_;
    QString clock_time_;
};

}  // namespace CANoeLite::ViewModels
