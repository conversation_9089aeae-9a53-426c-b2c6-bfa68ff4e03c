C:\msys64\ucrt64\bin\cmake.exe -E rm -f CMakeFiles\CANoeLite.dir/objects.a
C:\msys64\ucrt64\bin\ar.exe qc CMakeFiles\CANoeLite.dir/objects.a @CMakeFiles\CANoeLite.dir\objects1.rsp
C:\msys64\ucrt64\bin\c++.exe -O3 -DNDEBUG -Wl,--whole-archive CMakeFiles\CANoeLite.dir/objects.a -Wl,--no-whole-archive -o CANoeLite.exe -Wl,--out-implib,libCANoeLite.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\CANoeLite.dir\linkLibs.rsp
