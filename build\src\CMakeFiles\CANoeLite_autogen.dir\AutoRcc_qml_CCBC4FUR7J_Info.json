{"BUILD_DIR": "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/build/src/CANoeLite_autogen", "CMAKE_BINARY_DIR": "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/build", "CMAKE_CURRENT_BINARY_DIR": "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/build/src", "CMAKE_CURRENT_SOURCE_DIR": "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src", "CMAKE_SOURCE_DIR": "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite", "CROSS_CONFIG": false, "GENERATOR": "MinGW Makefiles", "INCLUDE_DIR": "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/build/src/CANoeLite_autogen/include", "INPUTS": ["C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/qml/MainWindow.qml", "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/qml/main.qml", "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/qml/dock/DockArea.qml", "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/qml/dock/TraceDockWidget.qml", "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/qml/dock/DockWidget.qml", "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/qml/dock/DockSplitter.qml", "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/qml/status/StatusBar.qml", "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/qml/status/StatusPanel.qml", "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/qml/status/StatusClock.qml", "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/qml/ribbon/RibbonPanel.qml", "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/qml/ribbon/RibbonView.qml", "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/qml/ribbon/QuickAccessToolbar.qml", "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/qml/ribbon/RibbonTab.qml", "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/qml/ribbon/RibbonButton.qml", "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/qml/components/ModernPanel.qml", "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/qml/components/ModernSeparator.qml", "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/qml/components/ModernProgressBar.qml", "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/qml/components/ModernButton.qml", "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/qml/components/ModernTab.qml", "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/qml/theme/Colors.qml", "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/qml/theme/Animations.qml", "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/qml/theme/AppTheme.qml", "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/qml/theme/Typography.qml", "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/qml/desktop/DesktopTabs.qml", "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/qml/desktop/DesktopTab.qml"], "LOCK_FILE": "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/build/src/CMakeFiles/CANoeLite_autogen.dir/AutoRcc_qml_CCBC4FUR7J_Lock.lock", "MULTI_CONFIG": false, "OPTIONS": ["-name", "qml"], "OUTPUT_CHECKSUM": "CCBC4FUR7J", "OUTPUT_NAME": "qrc_qml.cpp", "RCC_EXECUTABLE": "C:/msys64/ucrt64/share/qt6/bin/rcc.exe", "RCC_LIST_OPTIONS": ["--list"], "SETTINGS_FILE": "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/build/src/CMakeFiles/CANoeLite_autogen.dir/AutoRcc_qml_CCBC4FUR7J_Used.txt", "SOURCE": "C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/src/qml/qml.qrc", "USE_BETTER_GRAPH": true, "VERBOSITY": 0}