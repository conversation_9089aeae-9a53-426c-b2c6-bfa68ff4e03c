# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = C:\msys64\ucrt64\bin\cmake.exe

# The command to remove a file.
RM = C:\msys64\ucrt64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\work\01.LAB_ADAS_NW_Tool\CANoeLite

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build

# Utility rule file for CANoeLite_autogen.

# Include any custom commands dependencies for this target.
include src/CMakeFiles/CANoeLite_autogen.dir/compiler_depend.make

# Include the progress variables for this target.
include src/CMakeFiles/CANoeLite_autogen.dir/progress.make

src/CMakeFiles/CANoeLite_autogen: src/CANoeLite_autogen/timestamp

src/CANoeLite_autogen/timestamp: C:/msys64/ucrt64/share/qt6/bin/moc.exe
src/CANoeLite_autogen/timestamp: C:/msys64/ucrt64/share/qt6/bin/uic.exe
src/CANoeLite_autogen/timestamp: src/CMakeFiles/CANoeLite_autogen.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Automatic MOC and UIC for target CANoeLite"
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build\src && C:\msys64\ucrt64\bin\cmake.exe -E cmake_autogen C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/build/src/CMakeFiles/CANoeLite_autogen.dir/AutogenInfo.json Release
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build\src && C:\msys64\ucrt64\bin\cmake.exe -E touch C:/work/01.LAB_ADAS_NW_Tool/CANoeLite/build/src/CANoeLite_autogen/timestamp

src/CMakeFiles/CANoeLite_autogen.dir/codegen:
.PHONY : src/CMakeFiles/CANoeLite_autogen.dir/codegen

CANoeLite_autogen: src/CANoeLite_autogen/timestamp
CANoeLite_autogen: src/CMakeFiles/CANoeLite_autogen
CANoeLite_autogen: src/CMakeFiles/CANoeLite_autogen.dir/build.make
.PHONY : CANoeLite_autogen

# Rule to build all files generated by this target.
src/CMakeFiles/CANoeLite_autogen.dir/build: CANoeLite_autogen
.PHONY : src/CMakeFiles/CANoeLite_autogen.dir/build

src/CMakeFiles/CANoeLite_autogen.dir/clean:
	cd /d C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build\src && $(CMAKE_COMMAND) -P CMakeFiles\CANoeLite_autogen.dir\cmake_clean.cmake
.PHONY : src/CMakeFiles/CANoeLite_autogen.dir/clean

src/CMakeFiles/CANoeLite_autogen.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" C:\work\01.LAB_ADAS_NW_Tool\CANoeLite C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\src C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build\src C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build\src\CMakeFiles\CANoeLite_autogen.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : src/CMakeFiles/CANoeLite_autogen.dir/depend

