import QtQuick 2.15
import QtQuick.Controls 2.15
import "../theme"

DockWidget {
    id: root
    
    title: "Trace View"
    
    content: Component {
        Rectangle {
            color: Colors.surface
            
            Column {
                anchors.fill: parent
                anchors.margins: AppTheme.spacing.md
                spacing: AppTheme.spacing.md
                
                // Header
                Text {
                    text: "CAN Trace Messages"
                    font.family: AppTheme.typography.primaryFont
                    font.pixelSize: AppTheme.typography.titleMedium
                    font.weight: AppTheme.typography.weightMedium
                    color: Colors.onSurface
                }
                
                // Trace table placeholder
                Rectangle {
                    width: parent.width
                    height: parent.height - parent.spacing - 30
                    color: Colors.background
                    border.color: Colors.border
                    border.width: AppTheme.border.widthThin
                    radius: AppTheme.radius.sm
                    
                    ScrollView {
                        anchors.fill: parent
                        anchors.margins: AppTheme.spacing.sm
                        
                        Column {
                            spacing: 2
                            
                            // Header row
                            Rectangle {
                                width: parent.width
                                height: 30
                                color: Colors.primary
                                
                                Row {
                                    anchors.fill: parent
                                    anchors.margins: AppTheme.spacing.sm
                                    spacing: AppTheme.spacing.md
                                    
                                    Text {
                                        text: "Time"
                                        color: Colors.onPrimary
                                        font.weight: Font.Bold
                                        width: 80
                                    }
                                    Text {
                                        text: "ID"
                                        color: Colors.onPrimary
                                        font.weight: Font.Bold
                                        width: 60
                                    }
                                    Text {
                                        text: "Data"
                                        color: Colors.onPrimary
                                        font.weight: Font.Bold
                                        width: 200
                                    }
                                    Text {
                                        text: "Length"
                                        color: Colors.onPrimary
                                        font.weight: Font.Bold
                                        width: 60
                                    }
                                }
                            }
                            
                            // Sample data rows
                            Repeater {
                                model: 10
                                
                                Rectangle {
                                    width: parent.width
                                    height: 25
                                    color: index % 2 === 0 ? Colors.surface : Colors.surfaceVariant
                                    
                                    Row {
                                        anchors.fill: parent
                                        anchors.margins: AppTheme.spacing.sm
                                        spacing: AppTheme.spacing.md
                                        
                                        Text {
                                            text: (1000 + index * 10) + "ms"
                                            color: Colors.onSurface
                                            width: 80
                                        }
                                        Text {
                                            text: "0x" + (0x100 + index).toString(16).toUpperCase()
                                            color: Colors.onSurface
                                            width: 60
                                        }
                                        Text {
                                            text: "01 02 03 04 05 06 07 08"
                                            color: Colors.onSurface
                                            width: 200
                                        }
                                        Text {
                                            text: "8"
                                            color: Colors.onSurface
                                            width: 60
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
