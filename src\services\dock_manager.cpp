#include "services/dock_manager.h"

#include <QApplication>

namespace CANoeLite::Services {

DockManager::DockManager(std::shared_ptr<Core::ISettingsService> settings_service, QObject* parent)
    : QObject(parent), settings_service_(settings_service), main_window_(nullptr) {}

void DockManager::set_main_window(QMainWindow* main_window) {
    main_window_ = main_window;
}

void DockManager::register_dock_widget(const QString& id, QWidget* widget) {
    if (dock_widgets_.contains(id)) {
        return;
    }

    auto* dock_widget = new QDockWidget(widget->windowTitle(), main_window_);
    dock_widget->setWidget(widget);
    dock_widget->setObjectName(id);

    // Connect close signal
    connect(dock_widget, &QDockWidget::destroyed, this, &DockManager::on_dock_widget_closed);

    dock_widgets_[id] = dock_widget;
    main_window_->addDockWidget(Qt::RightDockArea, dock_widget);

    emit dock_widget_created(id, dock_widget);
}

void DockManager::show_dock_widget(const QString& id) {
    if (auto it = dock_widgets_.find(id); it != dock_widgets_.end()) {
        it.value()->show();
        it.value()->raise();
        it.value()->activateWindow();
    }
}

void DockManager::hide_dock_widget(const QString& id) {
    if (auto it = dock_widgets_.find(id); it != dock_widgets_.end()) {
        it.value()->hide();
    }
}

void DockManager::close_dock_widget(const QString& id) {
    if (auto it = dock_widgets_.find(id); it != dock_widgets_.end()) {
        it.value()->close();
        dock_widgets_.erase(it);
        emit dock_widget_closed(id);
    }
}

void DockManager::save_layout() {
    if (!main_window_) {
        return;
    }

    QByteArray layout_data = main_window_->saveState();
    settings_service_->set_value("window/layout", layout_data);
    settings_service_->set_value("window/geometry", main_window_->saveGeometry());
    settings_service_->sync();
}

void DockManager::restore_layout() {
    if (!main_window_) {
        return;
    }

    QByteArray layout_data = settings_service_->get_value("window/layout").toByteArray();
    QByteArray geometry_data = settings_service_->get_value("window/geometry").toByteArray();

    if (!layout_data.isEmpty()) {
        main_window_->restoreState(layout_data);
    }

    if (!geometry_data.isEmpty()) {
        main_window_->restoreGeometry(geometry_data);
    }
}

QDockWidget* DockManager::create_dock_widget(const QString& id, const QString& title, QWidget* content) {
    if (dock_widgets_.contains(id)) {
        show_dock_widget(id);
        return dock_widgets_[id];
    }

    content->setWindowTitle(title);
    register_dock_widget(id, content);
    show_dock_widget(id);

    return dock_widgets_[id];
}

void DockManager::on_dock_widget_closed() {
    auto* dock_widget = qobject_cast<QDockWidget*>(sender());
    if (!dock_widget) {
        return;
    }

    QString id = dock_widget->objectName();
    dock_widgets_.remove(id);
    emit dock_widget_closed(id);
}

}  // namespace CANoeLite::Services
