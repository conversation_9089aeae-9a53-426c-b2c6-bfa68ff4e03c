import QtQuick 2.15
import QtQuick.Window 2.15
import QtQuick.Controls 2.15
import "theme"

ApplicationWindow {
    id: window
    
    width: 1200
    height: 800
    minimumWidth: 800
    minimumHeight: 600
    visible: true
    title: "CANoeLite - Modern CAN Analysis Tool"
    
    // Apply theme colors
    color: Colors.background
    
    // Main window content
    MainWindow {
        id: mainWindow
        anchors.fill: parent
    }
    
    // Global shortcuts and behaviors
    Component.onCompleted: {
        // Initialize application
        console.log("CANoeLite QML application started")
    }
    
    onClosing: {
        // Save window state before closing
        if (mainWindow.viewModel) {
            mainWindow.viewModel.saveWindowState()
        }
    }
}
