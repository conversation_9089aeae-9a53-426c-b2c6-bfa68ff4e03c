import QtQuick 2.15
import "../theme"

StatusPanel {
    id: root
    
    property var viewModel: null
    
    text: getCurrentTime()
    textColor: Colors.statusAccent
    
    function getCurrentTime() {
        if (viewModel && viewModel.clockTime) {
            return viewModel.clockTime
        }
        return Qt.formatTime(new Date(), "hh:mm:ss")
    }
    
    Timer {
        interval: 1000
        running: true
        repeat: true
        onTriggered: {
            if (viewModel) {
                viewModel.update_clock()
            }
            root.text = getCurrentTime()
        }
    }
    
    // Connections to ViewModel
    Connections {
        target: viewModel
        
        function onClockTimeChanged() {
            root.text = getCurrentTime()
        }
    }
}
