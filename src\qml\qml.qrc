<RCC>
    <qresource prefix="/qml">
        <!-- Main Application -->
        <file>main.qml</file>
        <file>MainWindow.qml</file>
        
        <!-- Theme System -->
        <file>theme/AppTheme.qml</file>
        <file>theme/Colors.qml</file>
        <file>theme/Typography.qml</file>
        <file>theme/Animations.qml</file>
        
        <!-- Components -->
        <file>components/ModernButton.qml</file>
        <file>components/ModernTab.qml</file>
        <file>components/ModernPanel.qml</file>
        <file>components/ModernProgressBar.qml</file>
        <file>components/ModernSeparator.qml</file>
        
        <!-- Ribbon Interface -->
        <file>ribbon/RibbonView.qml</file>
        <file>ribbon/RibbonTab.qml</file>
        <file>ribbon/RibbonPanel.qml</file>
        <file>ribbon/RibbonButton.qml</file>
        <file>ribbon/QuickAccessToolbar.qml</file>
        
        <!-- Dock System -->
        <file>dock/DockArea.qml</file>
        <file>dock/DockWidget.qml</file>
        <file>dock/DockSplitter.qml</file>
        <file>dock/TraceDockWidget.qml</file>
        
        <!-- Desktop Tabs -->
        <file>desktop/DesktopTabs.qml</file>
        <file>desktop/DesktopTab.qml</file>
        
        <!-- Status Bar -->
        <file>status/StatusBar.qml</file>
        <file>status/StatusPanel.qml</file>
        <file>status/StatusClock.qml</file>
    </qresource>
</RCC>
