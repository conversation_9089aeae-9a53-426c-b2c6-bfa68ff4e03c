# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = C:\msys64\ucrt64\bin\cmake.exe

# The command to remove a file.
RM = C:\msys64\ucrt64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\work\01.LAB_ADAS_NW_Tool\CANoeLite

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: src/all
.PHONY : all

# The main recursive "codegen" target.
codegen: src/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall: src/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: src/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory src

# Recursive "all" directory target.
src/all: src/CMakeFiles/CANoeLite.dir/all
.PHONY : src/all

# Recursive "codegen" directory target.
src/codegen: src/CMakeFiles/CANoeLite.dir/codegen
.PHONY : src/codegen

# Recursive "preinstall" directory target.
src/preinstall:
.PHONY : src/preinstall

# Recursive "clean" directory target.
src/clean: src/CMakeFiles/CANoeLite.dir/clean
src/clean: src/CMakeFiles/CANoeLite_autogen_timestamp_deps.dir/clean
src/clean: src/CMakeFiles/CANoeLite_autogen.dir/clean
.PHONY : src/clean

#=============================================================================
# Target rules for target src/CMakeFiles/CANoeLite.dir

# All Build rule for target.
src/CMakeFiles/CANoeLite.dir/all: src/CMakeFiles/CANoeLite_autogen_timestamp_deps.dir/all
src/CMakeFiles/CANoeLite.dir/all: src/CMakeFiles/CANoeLite_autogen.dir/all
	$(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/depend
	$(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build\CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13,14 "Built target CANoeLite"
.PHONY : src/CMakeFiles/CANoeLite.dir/all

# Build rule for subdir invocation for target.
src/CMakeFiles/CANoeLite.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build\CMakeFiles 15
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 src/CMakeFiles/CANoeLite.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build\CMakeFiles 0
.PHONY : src/CMakeFiles/CANoeLite.dir/rule

# Convenience name for target.
CANoeLite: src/CMakeFiles/CANoeLite.dir/rule
.PHONY : CANoeLite

# codegen rule for target.
src/CMakeFiles/CANoeLite.dir/codegen: src/CMakeFiles/CANoeLite_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build\CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13,14 "Finished codegen for target CANoeLite"
.PHONY : src/CMakeFiles/CANoeLite.dir/codegen

# clean rule for target.
src/CMakeFiles/CANoeLite.dir/clean:
	$(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite.dir\build.make src/CMakeFiles/CANoeLite.dir/clean
.PHONY : src/CMakeFiles/CANoeLite.dir/clean

#=============================================================================
# Target rules for target src/CMakeFiles/CANoeLite_autogen_timestamp_deps.dir

# All Build rule for target.
src/CMakeFiles/CANoeLite_autogen_timestamp_deps.dir/all:
	$(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite_autogen_timestamp_deps.dir\build.make src/CMakeFiles/CANoeLite_autogen_timestamp_deps.dir/depend
	$(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite_autogen_timestamp_deps.dir\build.make src/CMakeFiles/CANoeLite_autogen_timestamp_deps.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build\CMakeFiles --progress-num= "Built target CANoeLite_autogen_timestamp_deps"
.PHONY : src/CMakeFiles/CANoeLite_autogen_timestamp_deps.dir/all

# Build rule for subdir invocation for target.
src/CMakeFiles/CANoeLite_autogen_timestamp_deps.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build\CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 src/CMakeFiles/CANoeLite_autogen_timestamp_deps.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build\CMakeFiles 0
.PHONY : src/CMakeFiles/CANoeLite_autogen_timestamp_deps.dir/rule

# Convenience name for target.
CANoeLite_autogen_timestamp_deps: src/CMakeFiles/CANoeLite_autogen_timestamp_deps.dir/rule
.PHONY : CANoeLite_autogen_timestamp_deps

# codegen rule for target.
src/CMakeFiles/CANoeLite_autogen_timestamp_deps.dir/codegen:
	$(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite_autogen_timestamp_deps.dir\build.make src/CMakeFiles/CANoeLite_autogen_timestamp_deps.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build\CMakeFiles --progress-num= "Finished codegen for target CANoeLite_autogen_timestamp_deps"
.PHONY : src/CMakeFiles/CANoeLite_autogen_timestamp_deps.dir/codegen

# clean rule for target.
src/CMakeFiles/CANoeLite_autogen_timestamp_deps.dir/clean:
	$(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite_autogen_timestamp_deps.dir\build.make src/CMakeFiles/CANoeLite_autogen_timestamp_deps.dir/clean
.PHONY : src/CMakeFiles/CANoeLite_autogen_timestamp_deps.dir/clean

#=============================================================================
# Target rules for target src/CMakeFiles/CANoeLite_autogen.dir

# All Build rule for target.
src/CMakeFiles/CANoeLite_autogen.dir/all: src/CMakeFiles/CANoeLite_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite_autogen.dir\build.make src/CMakeFiles/CANoeLite_autogen.dir/depend
	$(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite_autogen.dir\build.make src/CMakeFiles/CANoeLite_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build\CMakeFiles --progress-num=15 "Built target CANoeLite_autogen"
.PHONY : src/CMakeFiles/CANoeLite_autogen.dir/all

# Build rule for subdir invocation for target.
src/CMakeFiles/CANoeLite_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build\CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 src/CMakeFiles/CANoeLite_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build\CMakeFiles 0
.PHONY : src/CMakeFiles/CANoeLite_autogen.dir/rule

# Convenience name for target.
CANoeLite_autogen: src/CMakeFiles/CANoeLite_autogen.dir/rule
.PHONY : CANoeLite_autogen

# codegen rule for target.
src/CMakeFiles/CANoeLite_autogen.dir/codegen: src/CMakeFiles/CANoeLite_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite_autogen.dir\build.make src/CMakeFiles/CANoeLite_autogen.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=C:\work\01.LAB_ADAS_NW_Tool\CANoeLite\build\CMakeFiles --progress-num=15 "Finished codegen for target CANoeLite_autogen"
.PHONY : src/CMakeFiles/CANoeLite_autogen.dir/codegen

# clean rule for target.
src/CMakeFiles/CANoeLite_autogen.dir/clean:
	$(MAKE) $(MAKESILENT) -f src\CMakeFiles\CANoeLite_autogen.dir\build.make src/CMakeFiles/CANoeLite_autogen.dir/clean
.PHONY : src/CMakeFiles/CANoeLite_autogen.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

