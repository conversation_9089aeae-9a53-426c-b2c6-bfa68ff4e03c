#pragma once

#include <QDockWidget>
#include <QHash>
#include <QMainWindow>
#include <memory>

#include "core/interfaces.h"

namespace CANoeLite::Services {

/**
 * @brief Service for managing dock widgets and window layouts
 */
class DockManager : public QObject, public Core::IDockManager {
    Q_OBJECT

public:
    explicit DockManager(std::shared_ptr<Core::ISettingsService> settings_service, QObject* parent = nullptr);
    ~DockManager() override = default;

    /**
     * @brief Set the main window for dock management
     */
    void set_main_window(QMainWindow* main_window);

    // IDockManager implementation
    void register_dock_widget(const QString& id, QWidget* widget) override;
    void show_dock_widget(const QString& id) override;
    void hide_dock_widget(const QString& id) override;
    void close_dock_widget(const QString& id) override;
    void save_layout() override;
    void restore_layout() override;

    /**
     * @brief Create and show a new dock widget
     */
    QDockWidget* create_dock_widget(const QString& id, const QString& title, QWidget* content);

signals:
    void dock_widget_created(const QString& id, QDockWidget* dock_widget);
    void dock_widget_closed(const QString& id);

private slots:
    void on_dock_widget_closed();

private:
    std::shared_ptr<Core::ISettingsService> settings_service_;
    QMainWindow* main_window_;
    QHash<QString, QDockWidget*> dock_widgets_;
};

}  // namespace CANoeLite::Services
