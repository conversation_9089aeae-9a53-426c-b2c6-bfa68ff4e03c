import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../theme"
import "../components"

Rectangle {
    id: root
    
    property string title: ""
    property var buttons: []
    
    signal buttonClicked(string action)
    
    Layout.preferredWidth: Math.max(AppTheme.ribbon.panelMinWidth, contentLayout.implicitWidth + 2 * AppTheme.ribbon.panelPadding)
    Layout.fillHeight: true
    
    color: Colors.ribbonPanelBackground
    border.color: Colors.border
    border.width: AppTheme.border.widthThin
    radius: AppTheme.ribbon.borderRadius
    
    ColumnLayout {
        id: contentLayout
        anchors.fill: parent
        anchors.margins: AppTheme.ribbon.panelPadding
        spacing: AppTheme.spacing.sm
        
        // Panel content area
        Item {
            Layout.fillWidth: true
            Layout.fillHeight: true
            
            // Button grid layout
            GridLayout {
                anchors.fill: parent
                columns: Math.max(1, Math.floor(parent.width / (AppTheme.ribbon.buttonSize + AppTheme.ribbon.buttonSpacing)))
                rowSpacing: AppTheme.ribbon.buttonSpacing
                columnSpacing: AppTheme.ribbon.buttonSpacing
                
                Repeater {
                    model: root.buttons
                    
                    RibbonButton {
                        text: modelData.name || ""
                        iconSource: modelData.icon ? "qrc:/icons/" + modelData.icon + ".png" : ""
                        action: modelData.action || ""
                        
                        onClicked: {
                            root.buttonClicked(action)
                        }
                    }
                }
            }
        }
        
        // Panel title
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: AppTheme.spacing.lg
            color: Colors.separator
            radius: 2
            
            Text {
                anchors.centerIn: parent
                text: root.title
                font.family: AppTheme.typography.ribbon.fontFamily
                font.pixelSize: AppTheme.typography.ribbon.panelTitleSize
                font.weight: AppTheme.typography.ribbon.panelTitleWeight
                color: Colors.onSurface
                elide: Text.ElideRight
            }
        }
    }
}
