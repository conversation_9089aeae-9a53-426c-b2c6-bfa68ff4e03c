#include "services/ribbon_config_service.h"

#include <QDir>
#include <QFile>
#include <QJsonDocument>
#include <QJsonObject>
#include <QStandardPaths>

namespace CANoeLite::Services {

RibbonConfigService::RibbonConfigService(std::shared_ptr<Core::ISettingsService> settings_service)
    : settings_service_(settings_service) {}

void RibbonConfigService::load_configuration() {
    QString config_path = get_config_file_path();
    QFile file(config_path);

    if (!file.exists()) {
        load_default_configuration();
        save_configuration();
        return;
    }

    if (!file.open(QIODevice::ReadOnly)) {
        load_default_configuration();
        return;
    }

    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(file.readAll(), &error);
    file.close();

    if (error.error != QJsonParseError::NoError) {
        load_default_configuration();
        return;
    }

    ribbon_data_ = doc.object().toVariantMap();
}

void RibbonConfigService::save_configuration() {
    QString config_path = get_config_file_path();
    QDir().mkpath(QFileInfo(config_path).absolutePath());

    QFile file(config_path);
    if (!file.open(QIODevice::WriteOnly)) {
        return;
    }

    QJsonDocument doc = QJsonDocument::fromVariant(ribbon_data_);
    file.write(doc.toJson());
    file.close();
}

QVariantMap RibbonConfigService::get_ribbon_data() const {
    return ribbon_data_;
}

void RibbonConfigService::set_ribbon_data(const QVariantMap& data) {
    ribbon_data_ = data;
}

void RibbonConfigService::load_default_configuration() {
    QVariantMap tabs;

    // File tab
    QVariantMap file_tab;
    file_tab["name"] = "File";
    QVariantList file_panels;
    QVariantMap file_panel;
    file_panel["name"] = "File Operations";
    QVariantList file_buttons;
    file_buttons << QVariantMap{{"name", "New"}, {"icon", "new"}, {"action", "file_new"}};
    file_buttons << QVariantMap{{"name", "Open"}, {"icon", "open"}, {"action", "file_open"}};
    file_buttons << QVariantMap{{"name", "Save"}, {"icon", "save"}, {"action", "file_save"}};
    file_panel["buttons"] = file_buttons;
    file_panels << file_panel;
    file_tab["panels"] = file_panels;
    tabs["File"] = file_tab;

    // Analysis tab
    QVariantMap analysis_tab;
    analysis_tab["name"] = "Analysis";
    QVariantList analysis_panels;
    QVariantMap analysis_panel;
    analysis_panel["name"] = "Analysis Tools";
    QVariantList analysis_buttons;
    analysis_buttons << QVariantMap{{"name", "Trace"}, {"icon", "trace"}, {"action", "show_trace_view"}};
    analysis_buttons << QVariantMap{{"name", "Statistics"}, {"icon", "stats"}, {"action", "show_statistics"}};
    analysis_panel["buttons"] = analysis_buttons;
    analysis_panels << analysis_panel;
    analysis_tab["panels"] = analysis_panels;
    tabs["Analysis"] = analysis_tab;

    ribbon_data_["tabs"] = tabs;
}

QString RibbonConfigService::get_config_file_path() const {
    QString app_data_path = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    return QDir(app_data_path).filePath("ribbon.json");
}

}  // namespace CANoeLite::Services
