import QtQuick 2.15
import QtQuick.Controls 2.15
import "../theme"

TabButton {
    id: control
    
    // Custom properties
    property color activeColor: Colors.primary
    property color inactiveColor: Colors.secondaryDark
    property color hoverColor: Colors.hover
    property color textActiveColor: Colors.onPrimary
    property color textInactiveColor: Colors.onSecondary
    property bool closable: false
    
    signal closeRequested()
    
    implicitWidth: Math.max(AppTheme.tab.minWidth, contentItem.implicitWidth + leftPadding + rightPadding)
    implicitHeight: AppTheme.tab.height
    
    leftPadding: AppTheme.spacing.md
    rightPadding: closable ? AppTheme.spacing.sm : AppTheme.spacing.md
    topPadding: AppTheme.spacing.sm
    bottomPadding: AppTheme.spacing.sm
    
    background: Rectangle {
        id: backgroundRect
        radius: AppTheme.tab.borderRadius
        color: control.checked ? activeColor : 
               control.hovered ? hoverColor : inactiveColor
        
        // Smooth color transitions
        Behavior on color {
            ColorAnimation {
                duration: AppTheme.animations.hover.duration
                easing.type: AppTheme.animations.hover.easing
            }
        }
        
        // Active tab indicator
        Rectangle {
            anchors.bottom: parent.bottom
            width: parent.width
            height: 3
            color: activeColor
            visible: control.checked
            radius: 1.5
        }
    }
    
    contentItem: Row {
        spacing: AppTheme.spacing.sm
        
        Text {
            id: tabText
            text: control.text
            font.family: AppTheme.typography.tab.fontFamily
            font.pixelSize: AppTheme.typography.tab.fontSize
            font.weight: AppTheme.typography.tab.fontWeight
            font.letterSpacing: AppTheme.typography.tab.letterSpacing
            color: control.checked ? textActiveColor : textInactiveColor
            anchors.verticalCenter: parent.verticalCenter
            elide: Text.ElideRight
            
            Behavior on color {
                ColorAnimation {
                    duration: AppTheme.animations.hover.duration
                    easing.type: AppTheme.animations.hover.easing
                }
            }
        }
        
        // Close button
        Rectangle {
            id: closeButton
            visible: closable
            width: AppTheme.tab.closeButtonSize
            height: AppTheme.tab.closeButtonSize
            radius: width / 2
            color: closeMouseArea.containsMouse ? Colors.error : "transparent"
            anchors.verticalCenter: parent.verticalCenter
            
            Text {
                anchors.centerIn: parent
                text: "×"
                font.pixelSize: 12
                font.weight: Font.Bold
                color: closeMouseArea.containsMouse ? Colors.onPrimary : 
                       control.checked ? textActiveColor : textInactiveColor
            }
            
            MouseArea {
                id: closeMouseArea
                anchors.fill: parent
                hoverEnabled: true
                onClicked: {
                    mouse.accepted = true
                    control.closeRequested()
                }
            }
            
            Behavior on color {
                ColorAnimation {
                    duration: AppTheme.animations.hover.duration
                    easing.type: AppTheme.animations.hover.easing
                }
            }
        }
    }
    
    // Focus indicator
    Rectangle {
        anchors.fill: parent
        radius: AppTheme.tab.borderRadius
        color: "transparent"
        border.color: Colors.focus
        border.width: 2
        visible: control.activeFocus
        opacity: 0.6
    }
}
