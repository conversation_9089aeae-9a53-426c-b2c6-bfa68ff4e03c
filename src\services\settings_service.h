#pragma once

#include <QSettings>
#include <memory>

#include "core/interfaces.h"

namespace CANoeLite::Services {

/**
 * @brief Service for managing application settings using QSettings
 */
class SettingsService : public Core::ISettingsService {
public:
    SettingsService();
    ~SettingsService() override = default;

    // ISettingsService implementation
    QVariant get_value(const QString& key, const QVariant& default_value = QVariant()) const override;
    void set_value(const QString& key, const QVariant& value) override;
    void sync() override;

private:
    std::unique_ptr<QSettings> settings_;
};

}  // namespace CANoeLite::Services
