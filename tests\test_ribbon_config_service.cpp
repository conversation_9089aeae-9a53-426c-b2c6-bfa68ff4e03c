#include <catch2/catch_test_macros.hpp>

#include <QJsonDocument>
#include <QJsonObject>
#include <QTemporaryDir>

#include "services/ribbon_config_service.h"
#include "services/settings_service.h"

using namespace CANoeLite::Services;

TEST_CASE("RibbonConfigService", "[services]") {
    auto settings_service = std::make_shared<SettingsService>();
    RibbonConfigService service(settings_service);

    SECTION("Load default configuration") {
        service.load_configuration();
        QVariantMap data = service.get_ribbon_data();
        
        REQUIRE(!data.isEmpty());
        REQUIRE(data.contains("tabs"));
        
        QVariantMap tabs = data["tabs"].toMap();
        REQUIRE(tabs.contains("File"));
        REQUIRE(tabs.contains("Analysis"));
    }

    SECTION("Set and get ribbon data") {
        QVariantMap test_data;
        test_data["test_key"] = "test_value";
        
        service.set_ribbon_data(test_data);
        QVariantMap retrieved_data = service.get_ribbon_data();
        
        REQUIRE(retrieved_data["test_key"].toString() == "test_value");
    }
}
