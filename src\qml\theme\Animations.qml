pragma Singleton
import QtQuick 2.15

QtObject {
    // Duration Constants
    readonly property int durationFast: 150
    readonly property int durationMedium: 250
    readonly property int durationSlow: 350
    readonly property int durationVerySlow: 500
    
    // Easing Curves
    readonly property int easingStandard: Easing.OutCubic
    readonly property int easingDecelerate: Easing.OutQuart
    readonly property int easingAccelerate: Easing.InQuart
    readonly property int easingSharp: Easing.OutBack
    readonly property int easingBounce: Easing.OutBounce
    
    // Common Animation Properties
    readonly property QtObject hover: QtObject {
        readonly property int duration: durationFast
        readonly property int easing: easingStandard
    }
    
    readonly property QtObject press: QtObject {
        readonly property int duration: 100
        readonly property int easing: easingSharp
    }
    
    readonly property QtObject focus: QtObject {
        readonly property int duration: durationMedium
        readonly property int easing: easingStandard
    }
    
    readonly property QtObject slide: QtObject {
        readonly property int duration: durationMedium
        readonly property int easing: easingDecelerate
    }
    
    readonly property QtObject fade: QtObject {
        readonly property int duration: durationMedium
        readonly property int easing: easingStandard
    }
    
    readonly property QtObject scale: QtObject {
        readonly property int duration: durationFast
        readonly property int easing: easingStandard
    }
    
    readonly property QtObject ribbon: QtObject {
        readonly property int tabSwitchDuration: durationMedium
        readonly property int tabSwitchEasing: easingDecelerate
        readonly property int buttonHoverDuration: durationFast
        readonly property int buttonHoverEasing: easingStandard
        readonly property int collapseDuration: durationSlow
        readonly property int collapseEasing: easingDecelerate
    }
    
    readonly property QtObject dock: QtObject {
        readonly property int resizeDuration: durationMedium
        readonly property int resizeEasing: easingStandard
        readonly property int floatDuration: durationSlow
        readonly property int floatEasing: easingDecelerate
        readonly property int dockDuration: durationSlow
        readonly property int dockEasing: easingDecelerate
    }
    
    readonly property QtObject tab: QtObject {
        readonly property int switchDuration: durationMedium
        readonly property int switchEasing: easingStandard
        readonly property int closeDuration: durationFast
        readonly property int closeEasing: easingAccelerate
    }
    
    readonly property QtObject progress: QtObject {
        readonly property int updateDuration: durationMedium
        readonly property int updateEasing: easingStandard
        readonly property int pulseDuration: 1000
        readonly property int pulseEasing: easingStandard
    }
    
    // Helper functions for creating common animations
    function createHoverAnimation(target, property, fromValue, toValue) {
        return Qt.createQmlObject(`
            import QtQuick 2.15
            PropertyAnimation {
                target: ${target}
                property: "${property}"
                from: ${fromValue}
                to: ${toValue}
                duration: ${hover.duration}
                easing.type: ${hover.easing}
            }
        `, target)
    }
    
    function createFadeAnimation(target, fromOpacity, toOpacity) {
        return Qt.createQmlObject(`
            import QtQuick 2.15
            PropertyAnimation {
                target: ${target}
                property: "opacity"
                from: ${fromOpacity}
                to: ${toOpacity}
                duration: ${fade.duration}
                easing.type: ${fade.easing}
            }
        `, target)
    }
}
