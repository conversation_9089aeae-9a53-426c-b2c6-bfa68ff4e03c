#pragma once

#include <QObject>
#include <QString>

#include "core/interfaces.h"

namespace CANoeLite::ViewModels {

/**
 * @brief Base ViewModel for dock widgets
 */
class DockWidgetViewModel : public Core::IViewModel {
    Q_OBJECT

public:
    explicit DockWidgetViewModel(const QString& title, QObject* parent = nullptr);
    ~DockWidgetViewModel() override = default;

    /**
     * @brief Properties
     */
    QString title() const { return title_; }
    bool is_active() const { return is_active_; }

public slots:
    void set_title(const QString& title);
    void set_active(bool active);

signals:
    void title_changed(const QString& title);
    void active_changed(bool active);

private:
    QString title_;
    bool is_active_;
};

}  // namespace CANoeLite::ViewModels
