#pragma once

#include <QVariantMap>
#include <memory>

#include "core/interfaces.h"

namespace CANoeLite::Services {

/**
 * @brief Service for managing ribbon configuration from JSON
 */
class RibbonConfigService : public Core::IRibbonConfigService {
public:
    explicit RibbonConfigService(std::shared_ptr<Core::ISettingsService> settings_service);
    ~RibbonConfigService() override = default;

    // IRibbonConfigService implementation
    void load_configuration() override;
    void save_configuration() override;
    QVariantMap get_ribbon_data() const override;
    void set_ribbon_data(const QVariantMap& data) override;

private:
    void load_default_configuration();
    QString get_config_file_path() const;

    std::shared_ptr<Core::ISettingsService> settings_service_;
    QVariantMap ribbon_data_;
};

}  // namespace CANoeLite::Services
