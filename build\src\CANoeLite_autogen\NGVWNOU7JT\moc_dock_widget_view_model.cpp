/****************************************************************************
** Meta object code from reading C++ file 'dock_widget_view_model.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.1)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../src/view_models/dock_widget_view_model.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'dock_widget_view_model.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.1. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN9CANoeLite10ViewModels19DockWidgetViewModelE_t {};
} // unnamed namespace

template <> constexpr inline auto CANoeLite::ViewModels::DockWidgetViewModel::qt_create_metaobjectdata<qt_meta_tag_ZN9CANoeLite10ViewModels19DockWidgetViewModelE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "CANoeLite::ViewModels::DockWidgetViewModel",
        "title_changed",
        "",
        "title",
        "active_changed",
        "active",
        "set_title",
        "set_active"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'title_changed'
        QtMocHelpers::SignalData<void(const QString &)>(1, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 3 },
        }}),
        // Signal 'active_changed'
        QtMocHelpers::SignalData<void(bool)>(4, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 5 },
        }}),
        // Slot 'set_title'
        QtMocHelpers::SlotData<void(const QString &)>(6, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 3 },
        }}),
        // Slot 'set_active'
        QtMocHelpers::SlotData<void(bool)>(7, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 5 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<DockWidgetViewModel, qt_meta_tag_ZN9CANoeLite10ViewModels19DockWidgetViewModelE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject CANoeLite::ViewModels::DockWidgetViewModel::staticMetaObject = { {
    QMetaObject::SuperData::link<Core::IViewModel::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN9CANoeLite10ViewModels19DockWidgetViewModelE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN9CANoeLite10ViewModels19DockWidgetViewModelE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN9CANoeLite10ViewModels19DockWidgetViewModelE_t>.metaTypes,
    nullptr
} };

void CANoeLite::ViewModels::DockWidgetViewModel::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<DockWidgetViewModel *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->title_changed((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 1: _t->active_changed((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 2: _t->set_title((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 3: _t->set_active((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (DockWidgetViewModel::*)(const QString & )>(_a, &DockWidgetViewModel::title_changed, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (DockWidgetViewModel::*)(bool )>(_a, &DockWidgetViewModel::active_changed, 1))
            return;
    }
}

const QMetaObject *CANoeLite::ViewModels::DockWidgetViewModel::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *CANoeLite::ViewModels::DockWidgetViewModel::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN9CANoeLite10ViewModels19DockWidgetViewModelE_t>.strings))
        return static_cast<void*>(this);
    return Core::IViewModel::qt_metacast(_clname);
}

int CANoeLite::ViewModels::DockWidgetViewModel::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = Core::IViewModel::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 4)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 4)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 4;
    }
    return _id;
}

// SIGNAL 0
void CANoeLite::ViewModels::DockWidgetViewModel::title_changed(const QString & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 0, nullptr, _t1);
}

// SIGNAL 1
void CANoeLite::ViewModels::DockWidgetViewModel::active_changed(bool _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 1, nullptr, _t1);
}
QT_WARNING_POP
