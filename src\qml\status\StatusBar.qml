import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../theme"

Rectangle {
    id: root
    
    property var viewModel: null
    property string statusMessage: "Ready"
    property string messageType: "info"
    
    function initialize() {
        if (viewModel) {
            console.log("StatusBar initialized")
            updateFromViewModel()
        }
    }
    
    function updateFromViewModel() {
        if (viewModel) {
            statusMessage = viewModel.statusText || "Ready"
        }
    }
    
    function showMessage(message, type) {
        statusMessage = message
        messageType = type || "info"
        
        // Auto-clear message after 5 seconds
        messageTimer.restart()
    }
    
    color: Colors.statusBackground
    border.color: Colors.border
    border.width: AppTheme.border.widthThin
    
    RowLayout {
        anchors.fill: parent
        anchors.margins: AppTheme.spacing.sm
        spacing: AppTheme.spacing.md
        
        // Status message
        StatusPanel {
            Layout.fillWidth: true
            text: root.statusMessage
            textColor: getMessageColor()
        }
        
        // CAN Bitrate
        StatusPanel {
            Layout.preferredWidth: 120
            text: viewModel ? ("Bitrate: " + viewModel.canBitrate) : "Bitrate: 500k"
            textColor: Colors.statusText
        }
        
        // Clock
        StatusClock {
            Layout.preferredWidth: 100
            viewModel: root.viewModel
        }
    }
    
    function getMessageColor() {
        switch (messageType) {
            case "error": return Colors.error
            case "warning": return Colors.warning
            case "success": return Colors.success
            default: return Colors.statusText
        }
    }
    
    Timer {
        id: messageTimer
        interval: 5000
        onTriggered: {
            root.statusMessage = "Ready"
            root.messageType = "info"
        }
    }
    
    // Connections to ViewModel
    Connections {
        target: viewModel
        
        function onStatusTextChanged(text) {
            root.statusMessage = text
            root.messageType = "info"
        }
        
        function onCanBitrateChanged() {
            // Update is automatic through binding
        }
    }
}
