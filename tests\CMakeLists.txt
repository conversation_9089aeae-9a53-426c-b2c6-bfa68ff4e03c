# Test executable
set(TEST_SOURCES
    test_main.cpp
    test_ribbon_config_service.cpp
    test_dock_manager.cpp
    test_view_models.cpp
)

# Create test executable
add_executable(CANoeLite_Tests ${TEST_SOURCES})

# Link libraries
target_link_libraries(CANoeLite_Tests 
    Qt6::Core 
    Qt6::Widgets
    Catch2::Catch2WithMain
)

# Include source directories for testing
target_include_directories(CANoeLite_Tests PRIVATE 
    ${CMAKE_SOURCE_DIR}/src
)

# Add source files to test target (for testing private methods)
target_sources(CANoeLite_Tests PRIVATE
    ${CMAKE_SOURCE_DIR}/src/services/ribbon_config_service.cpp
    ${CMAKE_SOURCE_DIR}/src/services/settings_service.cpp
    ${CMAKE_SOURCE_DIR}/src/services/dock_manager.cpp
    ${CMAKE_SOURCE_DIR}/src/view_models/main_window_view_model.cpp
    ${CMAKE_SOURCE_DIR}/src/view_models/ribbon_view_model.cpp
    ${CMAKE_SOURCE_DIR}/src/view_models/dock_widget_view_model.cpp
)

# Enable testing
enable_testing()

# Add test
add_test(NAME CANoeLite_Tests COMMAND CANoeLite_Tests)
