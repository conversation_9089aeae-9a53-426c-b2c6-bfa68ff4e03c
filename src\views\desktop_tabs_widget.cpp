#include "views/desktop_tabs_widget.h"

#include <QLabel>
#include <QPushButton>
#include <QTabBar>
#include <QVBoxLayout>

namespace CANoeLite::Views {

DesktopTabsWidget::DesktopTabsWidget(QWidget* parent) : QTabWidget(parent), window_area_counter_(1) {
    setup_ui();
    create_default_area();
}

void DesktopTabsWidget::setup_ui() {
    setTabsClosable(true);
    setMovable(true);
    setTabPosition(QTabWidget::North);

    // Add "+" button for new tabs
    auto* add_button = new QPushButton("+");
    add_button->setFixedSize(24, 24);
    add_button->setToolTip("Add new window area");
    setCornerWidget(add_button, Qt::TopRightCorner);

    connect(this, &QTabWidget::tabCloseRequested, this, &DesktopTabsWidget::on_tab_close_requested);
    connect(add_button, &QPushButton::clicked, this, &DesktopTabsWidget::on_add_tab_clicked);
}

void DesktopTabsWidget::create_default_area() {
    add_window_area("Main Area");
}

void DesktopTabsWidget::add_window_area(const QString& name) {
    auto* area_widget = new QWidget();
    auto* layout = new QVBoxLayout(area_widget);

    auto* placeholder_label = new QLabel("Window Area: " + name);
    placeholder_label->setAlignment(Qt::AlignCenter);
    placeholder_label->setStyleSheet("color: #888; font-size: 14px;");
    layout->addWidget(placeholder_label);

    addTab(area_widget, name);
    setCurrentWidget(area_widget);
}

void DesktopTabsWidget::remove_window_area(int index) {
    if (index >= 0 && index < count() && count() > 1) {
        removeTab(index);
    }
}

void DesktopTabsWidget::on_tab_close_requested(int index) {
    remove_window_area(index);
}

void DesktopTabsWidget::on_add_tab_clicked() {
    QString area_name = QString("Area %1").arg(++window_area_counter_);
    add_window_area(area_name);
}

}  // namespace CANoeLite::Views
