import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../theme"

Item {
    id: root
    
    property var viewModel: null
    
    function initialize() {
        if (viewModel) {
            console.log("DockArea initialized")
            // Create default dock widgets
            createDefaultDockWidgets()
        }
    }
    
    function createDefaultDockWidgets() {
        // Create trace dock widget by default
        var traceDock = traceDockComponent.createObject(root)
        if (traceDock) {
            traceDock.anchors.fill = root
        }
    }
    
    // Main content area
    Rectangle {
        anchors.fill: parent
        color: Colors.dockBackground
        border.color: Colors.border
        border.width: AppTheme.border.widthThin
        
        // Placeholder content
        Column {
            anchors.centerIn: parent
            spacing: AppTheme.spacing.lg
            
            Text {
                text: "Main Content Area"
                font.family: AppTheme.typography.primaryFont
                font.pixelSize: AppTheme.typography.headlineMedium
                font.weight: AppTheme.typography.weightMedium
                color: Colors.onBackground
                anchors.horizontalCenter: parent.horizontalCenter
            }
            
            Text {
                text: "Dock widgets will be displayed here"
                font.family: AppTheme.typography.primaryFont
                font.pixelSize: AppTheme.typography.bodyMedium
                color: Colors.onBackground
                anchors.horizontalCenter: parent.horizontalCenter
            }
        }
    }
    
    // Component for creating trace dock widgets
    Component {
        id: traceDockComponent
        
        TraceDockWidget {
            viewModel: root.viewModel
        }
    }
}
