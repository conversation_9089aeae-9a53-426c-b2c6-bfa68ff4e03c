#pragma once

#include <QTableWidget>
#include <QWidget>
#include <memory>

namespace CANoeLite::ViewModels {
class DockWidgetViewModel;
}

namespace CANoeLite::Views {

/**
 * @brief Sample trace dock widget
 */
class TraceDockWidget : public QWidget {
    Q_OBJECT

public:
    explicit TraceDockWidget(QWidget* parent = nullptr);
    ~TraceDockWidget() override = default;

private slots:
    void add_sample_data();
    void clear_trace();

private:
    void setup_ui();
    void setup_sample_data();

    std::shared_ptr<ViewModels::DockWidgetViewModel> view_model_;
    QTableWidget* trace_table_;
};

}  // namespace CANoeLite::Views
