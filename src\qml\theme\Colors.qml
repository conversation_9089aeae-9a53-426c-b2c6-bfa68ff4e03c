pragma Singleton
import QtQuick 2.15

QtObject {
    // Primary Colors - Bordeaux Red Theme
    readonly property color primary: "#722F37"           // Deep bordeaux red
    readonly property color primaryLight: "#8B4A52"     // Lighter bordeaux
    readonly property color primaryDark: "#5A252B"      // Darker bordeaux
    readonly property color primaryAccent: "#A0434D"    // Accent bordeaux
    
    // Secondary Colors - Light Gray Theme
    readonly property color secondary: "#F5F5F5"        // Light gray background
    readonly property color secondaryLight: "#FFFFFF"   // Pure white
    readonly property color secondaryDark: "#E8E8E8"    // Darker gray
    readonly property color secondaryAccent: "#DADADA"  // Medium gray
    
    // Neutral Colors
    readonly property color background: "#F5F5F5"       // Main background
    readonly property color surface: "#FFFFFF"          // Card/panel surface
    readonly property color surfaceVariant: "#F8F8F8"   // Alternative surface
    
    // Text Colors
    readonly property color onPrimary: "#FFFFFF"        // Text on primary
    readonly property color onSecondary: "#2C2C2C"      // Text on secondary
    readonly property color onBackground: "#2C2C2C"     // Text on background
    readonly property color onSurface: "#2C2C2C"        // Text on surface
    
    // State Colors
    readonly property color hover: "#8B4A52"            // Hover state
    readonly property color pressed: "#5A252B"          // Pressed state
    readonly property color selected: "#722F37"         // Selected state
    readonly property color disabled: "#CCCCCC"         // Disabled state
    readonly property color focus: "#A0434D"            // Focus outline
    
    // Semantic Colors
    readonly property color success: "#4CAF50"          // Success green
    readonly property color warning: "#FF9800"          // Warning orange
    readonly property color error: "#F44336"            // Error red
    readonly property color info: "#2196F3"             // Info blue
    
    // Border and Separator Colors
    readonly property color border: "#E0E0E0"           // Default border
    readonly property color borderLight: "#F0F0F0"      // Light border
    readonly property color borderDark: "#CCCCCC"       // Dark border
    readonly property color separator: "#E8E8E8"        // Separator lines
    
    // Shadow Colors
    readonly property color shadow: "#00000020"         // Light shadow
    readonly property color shadowMedium: "#00000040"   // Medium shadow
    readonly property color shadowDark: "#00000060"     // Dark shadow
    
    // Ribbon Specific Colors
    readonly property color ribbonBackground: "#F5F5F5"
    readonly property color ribbonTabActive: "#722F37"
    readonly property color ribbonTabInactive: "#E8E8E8"
    readonly property color ribbonTabHover: "#8B4A52"
    readonly property color ribbonPanelBackground: "#FFFFFF"
    readonly property color ribbonButtonHover: "#F0F0F0"
    
    // Dock Widget Colors
    readonly property color dockBackground: "#F5F5F5"
    readonly property color dockTitleBar: "#E8E8E8"
    readonly property color dockTitleBarActive: "#722F37"
    readonly property color dockSplitter: "#CCCCCC"
    readonly property color dockSplitterHover: "#722F37"
    
    // Status Bar Colors
    readonly property color statusBackground: "#E8E8E8"
    readonly property color statusText: "#2C2C2C"
    readonly property color statusAccent: "#722F37"
}
