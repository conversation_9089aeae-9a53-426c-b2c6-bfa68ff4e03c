import QtQuick 2.15
import "../theme"

Rectangle {
    id: root
    
    property real value: 0.0
    property real minimumValue: 0.0
    property real maximumValue: 1.0
    property color backgroundColor: Colors.secondaryDark
    property color progressColor: Colors.primary
    property bool indeterminate: false
    
    height: 4
    color: backgroundColor
    radius: height / 2
    
    Rectangle {
        id: progressRect
        height: parent.height
        radius: parent.radius
        color: progressColor
        
        width: indeterminate ? parent.width * 0.3 : 
               parent.width * ((root.value - root.minimumValue) / (root.maximumValue - root.minimumValue))
        
        // Indeterminate animation
        SequentialAnimation {
            running: indeterminate
            loops: Animation.Infinite
            
            NumberAnimation {
                target: progressRect
                property: "x"
                from: -progressRect.width
                to: root.width
                duration: 1500
                easing.type: Easing.InOutQuad
            }
        }
        
        // Value change animation
        Behavior on width {
            enabled: !indeterminate
            NumberAnimation {
                duration: AppTheme.animations.progress.updateDuration
                easing.type: AppTheme.animations.progress.updateEasing
            }
        }
    }
}
