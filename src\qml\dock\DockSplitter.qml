import QtQuick 2.15
import "../theme"

Rectangle {
    id: root
    
    property bool horizontal: true
    property real splitPosition: 0.5
    
    signal positionChanged(real position)
    
    width: horizontal ? AppTheme.dock.splitterSize : parent.width
    height: horizontal ? parent.height : AppTheme.dock.splitterSize
    color: mouseArea.containsMouse ? Colors.dockSplitterHover : Colors.dockSplitter
    
    MouseArea {
        id: mouseArea
        anchors.fill: parent
        hoverEnabled: true
        cursorShape: horizontal ? Qt.SplitHCursor : Qt.SplitVCursor
        
        property real startPosition: 0
        
        onPressed: {
            startPosition = horizontal ? mouse.x : mouse.y
        }
        
        onPositionChanged: {
            if (pressed) {
                var delta = (horizontal ? mouse.x : mouse.y) - startPosition
                var newPosition = splitPosition + (delta / (horizontal ? parent.width : parent.height))
                newPosition = Math.max(0.1, Math.min(0.9, newPosition))
                root.positionChanged(newPosition)
            }
        }
    }
    
    Behavior on color {
        ColorAnimation {
            duration: AppTheme.animations.hover.duration
            easing.type: AppTheme.animations.hover.easing
        }
    }
}
