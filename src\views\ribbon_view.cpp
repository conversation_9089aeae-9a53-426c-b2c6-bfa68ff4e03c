#include "views/ribbon_view.h"

#include <QGroupBox>
#include <QHBoxLayout>
#include <QMessageBox>
#include <QPushButton>
#include <QVBoxLayout>

#include "view_models/ribbon_view_model.h"

namespace CANoeLite::Views {

RibbonView::RibbonView(std::shared_ptr<ViewModels::RibbonViewModel> view_model, QWidget* parent)
    : QWidget(parent), view_model_(view_model) {
    setup_ui();
    setup_connections();
    create_ribbon_tabs();
}

void RibbonView::setup_ui() {
    setFixedHeight(120);
    setStyleSheet("RibbonView { background-color: #f0f0f0; border-bottom: 1px solid #d0d0d0; }");

    auto* layout = new QVBoxLayout(this);
    layout->setContentsMargins(5, 5, 5, 5);
    layout->setSpacing(2);

    // Quick access toolbar
    auto* quick_access_layout = new QHBoxLayout();
    quick_access_button_ = new QToolButton(this);
    quick_access_button_->setText("⚙");
    quick_access_button_->setToolTip("Quick Access / Customize Ribbon");
    quick_access_button_->setFixedSize(24, 24);
    quick_access_layout->addWidget(quick_access_button_);
    quick_access_layout->addStretch();

    layout->addLayout(quick_access_layout);

    // Tab widget for ribbon tabs
    tab_widget_ = new QTabWidget(this);
    tab_widget_->setTabPosition(QTabWidget::North);
    layout->addWidget(tab_widget_);
}

void RibbonView::setup_connections() {
    connect(quick_access_button_, &QToolButton::clicked, this, &RibbonView::on_quick_access_clicked);
    connect(tab_widget_, &QTabWidget::currentChanged, this, &RibbonView::on_tab_changed);
}

void RibbonView::create_ribbon_tabs() {
    QVariantMap ribbon_data = view_model_->ribbon_data();
    QVariantMap tabs = ribbon_data["tabs"].toMap();

    // Define tab order
    QStringList tab_order = {"File", "Home", "Analysis", "Simulation", "Test", "Diagnostics", "Environment",
                             "Hardware", "Tools", "Layout"};

    for (const QString& tab_name : tab_order) {
        if (tabs.contains(tab_name)) {
            create_tab_content(tab_name, tabs[tab_name].toMap());
        } else {
            // Create empty tab for missing tabs
            auto* empty_widget = new QWidget();
            tab_widget_->addTab(empty_widget, tab_name);
        }
    }
}

void RibbonView::create_tab_content(const QString& tab_name, const QVariantMap& tab_data) {
    auto* tab_widget = new QWidget();
    auto* tab_layout = new QHBoxLayout(tab_widget);
    tab_layout->setContentsMargins(5, 5, 5, 5);

    QVariantList panels = tab_data["panels"].toList();
    for (const QVariant& panel_variant : panels) {
        QVariantMap panel_data = panel_variant.toMap();
        QWidget* panel = create_panel(panel_data);
        tab_layout->addWidget(panel);
    }

    tab_layout->addStretch();
    tab_widget_->addTab(tab_widget, tab_name);
}

QWidget* RibbonView::create_panel(const QVariantMap& panel_data) {
    QString panel_name = panel_data["name"].toString();
    QVariantList buttons = panel_data["buttons"].toList();

    auto* group_box = new QGroupBox(panel_name);
    auto* layout = new QHBoxLayout(group_box);

    for (const QVariant& button_variant : buttons) {
        QVariantMap button_data = button_variant.toMap();
        QString button_name = button_data["name"].toString();
        QString action = button_data["action"].toString();

        auto* button = new QPushButton(button_name);
        button->setProperty("action", action);
        button->setMinimumSize(60, 40);
        connect(button, &QPushButton::clicked, this, &RibbonView::on_button_clicked);

        layout->addWidget(button);
    }

    return group_box;
}

void RibbonView::on_quick_access_clicked() {
    QMessageBox::information(this, "Quick Access", "Customize Ribbon dialog would open here.");
}

void RibbonView::on_tab_changed(int index) {
    if (index >= 0 && index < tab_widget_->count()) {
        QString tab_name = tab_widget_->tabText(index);
        view_model_->set_current_tab(tab_name);
    }
}

void RibbonView::on_button_clicked() {
    auto* button = qobject_cast<QPushButton*>(sender());
    if (!button) {
        return;
    }

    QString action = button->property("action").toString();
    if (!action.isEmpty()) {
        view_model_->execute_action(action);
    }
}

}  // namespace CANoeLite::Views
