#include <catch2/catch_test_macros.hpp>

#include <QApplication>

/**
 * @brief Test main for CANoeLite unit tests
 */

// Global QApplication instance for Qt-based tests
static QApplication* test_app = nullptr;

struct TestSetup {
    TestSetup() {
        if (!test_app) {
            int argc = 1;
            char* argv[] = {"test"};
            test_app = new QApplication(argc, argv);
        }
    }
    
    ~TestSetup() {
        // Don't delete QApplication here as it might be needed for other tests
    }
};

// Global test setup
static TestSetup test_setup;
