#pragma once

#include <QObject>
#include <memory>

#include "core/interfaces.h"

namespace CANoeLite::Services {
class DockManager;
class RibbonConfigService;
class SettingsService;
}  // namespace CANoeLite::Services

namespace CANoeLite::ViewModels {
class MainWindowViewModel;
}

namespace CANoeLite::Core {

/**
 * @brief Main application class that manages dependency injection and application lifecycle
 */
class Application : public QObject {
    Q_OBJECT

public:
    explicit Application(QObject* parent = nullptr);
    ~Application() override;

    /**
     * @brief Initialize the application services and view models
     */
    bool initialize();

    /**
     * @brief Service locator methods
     */
    std::shared_ptr<Services::DockManager> dock_manager() const { return dock_manager_; }
    std::shared_ptr<Services::RibbonConfigService> ribbon_config_service() const { return ribbon_config_service_; }
    std::shared_ptr<Services::SettingsService> settings_service() const { return settings_service_; }

    /**
     * @brief Get the main window view model for QML
     */
    std::shared_ptr<ViewModels::MainWindowViewModel> main_window_view_model() const { return main_window_view_model_; }

private:
    void setup_services();
    void setup_view_models();

    // Services
    std::shared_ptr<Services::DockManager> dock_manager_;
    std::shared_ptr<Services::RibbonConfigService> ribbon_config_service_;
    std::shared_ptr<Services::SettingsService> settings_service_;

    // ViewModels
    std::shared_ptr<ViewModels::MainWindowViewModel> main_window_view_model_;
};

}  // namespace CANoeLite::Core
