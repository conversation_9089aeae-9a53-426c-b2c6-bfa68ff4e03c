#pragma once

#include <QApplication>
#include <memory>

#include "core/interfaces.h"

namespace CANoeLite::Services {
class DockManager;
class RibbonConfigService;
class SettingsService;
}  // namespace CANoeLite::Services

namespace CANoeLite::ViewModels {
class MainWindowViewModel;
}

namespace CANoeLite::Views {
class MainWindow;
}

namespace CANoeLite::Core {

/**
 * @brief Main application class that manages dependency injection and application lifecycle
 */
class Application : public QApplication {
    Q_OBJECT

public:
    explicit Application(int& argc, char** argv);
    ~Application() override;

    /**
     * @brief Initialize the application and show the main window
     */
    void initialize();

    /**
     * @brief Get the singleton application instance
     */
    static Application* instance();

    /**
     * @brief Service locator methods
     */
    std::shared_ptr<Services::DockManager> dock_manager() const { return dock_manager_; }
    std::shared_ptr<Services::RibbonConfigService> ribbon_config_service() const { return ribbon_config_service_; }
    std::shared_ptr<Services::SettingsService> settings_service() const { return settings_service_; }

private:
    void setup_services();
    void setup_view_models();
    void setup_main_window();

    // Services
    std::shared_ptr<Services::DockManager> dock_manager_;
    std::shared_ptr<Services::RibbonConfigService> ribbon_config_service_;
    std::shared_ptr<Services::SettingsService> settings_service_;

    // ViewModels
    std::shared_ptr<ViewModels::MainWindowViewModel> main_window_view_model_;

    // Main window
    Views::MainWindow* main_window_;

    static Application* instance_;
};

}  // namespace CANoeLite::Core
