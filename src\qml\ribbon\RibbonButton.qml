import QtQuick 2.15
import QtQuick.Controls 2.15
import QtGraphicalEffects 1.15
import "../theme"

Button {
    id: control
    
    property string iconSource: ""
    property string action: ""
    property bool isLarge: true
    
    implicitWidth: isLarge ? AppTheme.ribbon.buttonSize : AppTheme.ribbon.buttonSize * 0.7
    implicitHeight: isLarge ? AppTheme.ribbon.buttonSize : AppTheme.ribbon.buttonSize * 0.7
    
    background: Rectangle {
        radius: AppTheme.radius.sm
        color: control.down ? Colors.pressed : 
               control.hovered ? Colors.ribbonButtonHover : "transparent"
        border.color: control.hovered ? Colors.border : "transparent"
        border.width: AppTheme.border.widthThin
        
        Behavior on color {
            ColorAnimation {
                duration: AppTheme.animations.ribbon.buttonHoverDuration
                easing.type: AppTheme.animations.ribbon.buttonHoverEasing
            }
        }
        
        Behavior on border.color {
            ColorAnimation {
                duration: AppTheme.animations.ribbon.buttonHoverDuration
                easing.type: AppTheme.animations.ribbon.buttonHoverEasing
            }
        }
    }
    
    contentItem: Column {
        spacing: AppTheme.spacing.xs
        
        // Icon
        Item {
            width: parent.width
            height: isLarge ? 32 : 24
            anchors.horizontalCenter: parent.horizontalCenter
            
            Image {
                id: iconImage
                anchors.centerIn: parent
                width: isLarge ? 32 : 24
                height: isLarge ? 32 : 24
                source: iconSource
                fillMode: Image.PreserveAspectFit
                visible: iconSource !== ""
                
                ColorOverlay {
                    anchors.fill: parent
                    source: parent
                    color: Colors.onSurface
                    visible: iconSource !== ""
                }
            }
            
            // Fallback icon if no image
            Rectangle {
                anchors.centerIn: parent
                width: isLarge ? 32 : 24
                height: isLarge ? 32 : 24
                radius: 4
                color: Colors.primary
                visible: iconSource === ""
                
                Text {
                    anchors.centerIn: parent
                    text: control.text.charAt(0).toUpperCase()
                    font.pixelSize: isLarge ? 16 : 12
                    font.weight: Font.Bold
                    color: Colors.onPrimary
                }
            }
        }
        
        // Text label
        Text {
            width: parent.width
            text: control.text
            font.family: AppTheme.typography.ribbon.fontFamily
            font.pixelSize: AppTheme.typography.ribbon.buttonFontSize
            font.weight: AppTheme.typography.ribbon.buttonFontWeight
            color: Colors.onSurface
            horizontalAlignment: Text.AlignHCenter
            wrapMode: Text.WordWrap
            maximumLineCount: 2
            elide: Text.ElideRight
        }
    }
    
    // Tooltip
    ToolTip.visible: hovered && ToolTip.text !== ""
    ToolTip.text: control.text
    ToolTip.delay: 1000
    
    // Focus indicator
    Rectangle {
        anchors.fill: parent
        radius: AppTheme.radius.sm
        color: "transparent"
        border.color: Colors.focus
        border.width: 2
        visible: control.activeFocus
        opacity: 0.6
    }
    
    // Press animation
    scale: pressed ? 0.95 : 1.0
    Behavior on scale {
        NumberAnimation {
            duration: AppTheme.animations.press.duration
            easing.type: AppTheme.animations.press.easing
        }
    }
}
