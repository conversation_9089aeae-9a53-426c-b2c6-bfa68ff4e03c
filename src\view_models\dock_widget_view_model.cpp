#include "view_models/dock_widget_view_model.h"

namespace CANoeLite::ViewModels {

DockWidgetViewModel::DockWidgetViewModel(const QString& title, QObject* parent)
    : Core::IViewModel(parent), title_(title), is_active_(false) {}

void DockWidgetViewModel::set_title(const QString& title) {
    if (title_ != title) {
        title_ = title;
        emit title_changed(title);
        emit property_changed("title", title);
    }
}

void DockWidgetViewModel::set_active(bool active) {
    if (is_active_ != active) {
        is_active_ = active;
        emit active_changed(active);
        emit property_changed("is_active", active);
    }
}

}  // namespace CANoeLite::ViewModels
