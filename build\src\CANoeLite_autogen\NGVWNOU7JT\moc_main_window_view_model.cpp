/****************************************************************************
** Meta object code from reading C++ file 'main_window_view_model.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.1)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../src/view_models/main_window_view_model.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'main_window_view_model.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.1. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN9CANoeLite10ViewModels19MainWindowViewModelE_t {};
} // unnamed namespace

template <> constexpr inline auto CANoeLite::ViewModels::MainWindowViewModel::qt_create_metaobjectdata<qt_meta_tag_ZN9CANoeLite10ViewModels19MainWindowViewModelE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "CANoeLite::ViewModels::MainWindowViewModel",
        "window_title_changed",
        "",
        "title",
        "status_text_changed",
        "text",
        "can_bitrate_changed",
        "bitrate",
        "clock_time_changed",
        "time",
        "showMessage",
        "message",
        "type",
        "windowTitleChanged",
        "set_window_title",
        "set_status_text",
        "set_can_bitrate",
        "update_clock",
        "executeCommand",
        "action",
        "showCustomizeRibbonDialog",
        "saveWindowState",
        "windowTitle",
        "statusText",
        "canBitrate",
        "clockTime",
        "ribbonViewModel"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'window_title_changed'
        QtMocHelpers::SignalData<void(const QString &)>(1, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 3 },
        }}),
        // Signal 'status_text_changed'
        QtMocHelpers::SignalData<void(const QString &)>(4, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 5 },
        }}),
        // Signal 'can_bitrate_changed'
        QtMocHelpers::SignalData<void(const QString &)>(6, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 7 },
        }}),
        // Signal 'clock_time_changed'
        QtMocHelpers::SignalData<void(const QString &)>(8, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 9 },
        }}),
        // Signal 'showMessage'
        QtMocHelpers::SignalData<void(const QString &, const QString &)>(10, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 11 }, { QMetaType::QString, 12 },
        }}),
        // Signal 'windowTitleChanged'
        QtMocHelpers::SignalData<void(const QString &)>(13, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 3 },
        }}),
        // Slot 'set_window_title'
        QtMocHelpers::SlotData<void(const QString &)>(14, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 3 },
        }}),
        // Slot 'set_status_text'
        QtMocHelpers::SlotData<void(const QString &)>(15, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 5 },
        }}),
        // Slot 'set_can_bitrate'
        QtMocHelpers::SlotData<void(const QString &)>(16, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 7 },
        }}),
        // Slot 'update_clock'
        QtMocHelpers::SlotData<void()>(17, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'executeCommand'
        QtMocHelpers::SlotData<void(const QString &)>(18, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 19 },
        }}),
        // Slot 'showCustomizeRibbonDialog'
        QtMocHelpers::SlotData<void()>(20, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'saveWindowState'
        QtMocHelpers::SlotData<void()>(21, 2, QMC::AccessPublic, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'windowTitle'
        QtMocHelpers::PropertyData<QString>(22, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable, 0),
        // property 'statusText'
        QtMocHelpers::PropertyData<QString>(23, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable, 1),
        // property 'canBitrate'
        QtMocHelpers::PropertyData<QString>(24, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable, 2),
        // property 'clockTime'
        QtMocHelpers::PropertyData<QString>(25, QMetaType::QString, QMC::DefaultPropertyFlags, 3),
        // property 'ribbonViewModel'
        QtMocHelpers::PropertyData<QObject*>(26, QMetaType::QObjectStar, QMC::DefaultPropertyFlags | QMC::Constant),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<MainWindowViewModel, qt_meta_tag_ZN9CANoeLite10ViewModels19MainWindowViewModelE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject CANoeLite::ViewModels::MainWindowViewModel::staticMetaObject = { {
    QMetaObject::SuperData::link<Core::IViewModel::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN9CANoeLite10ViewModels19MainWindowViewModelE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN9CANoeLite10ViewModels19MainWindowViewModelE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN9CANoeLite10ViewModels19MainWindowViewModelE_t>.metaTypes,
    nullptr
} };

void CANoeLite::ViewModels::MainWindowViewModel::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<MainWindowViewModel *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->window_title_changed((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 1: _t->status_text_changed((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 2: _t->can_bitrate_changed((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 3: _t->clock_time_changed((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 4: _t->showMessage((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 5: _t->windowTitleChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 6: _t->set_window_title((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 7: _t->set_status_text((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 8: _t->set_can_bitrate((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 9: _t->update_clock(); break;
        case 10: _t->executeCommand((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 11: _t->showCustomizeRibbonDialog(); break;
        case 12: _t->saveWindowState(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (MainWindowViewModel::*)(const QString & )>(_a, &MainWindowViewModel::window_title_changed, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (MainWindowViewModel::*)(const QString & )>(_a, &MainWindowViewModel::status_text_changed, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (MainWindowViewModel::*)(const QString & )>(_a, &MainWindowViewModel::can_bitrate_changed, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (MainWindowViewModel::*)(const QString & )>(_a, &MainWindowViewModel::clock_time_changed, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (MainWindowViewModel::*)(const QString & , const QString & )>(_a, &MainWindowViewModel::showMessage, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (MainWindowViewModel::*)(const QString & )>(_a, &MainWindowViewModel::windowTitleChanged, 5))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<QString*>(_v) = _t->window_title(); break;
        case 1: *reinterpret_cast<QString*>(_v) = _t->status_text(); break;
        case 2: *reinterpret_cast<QString*>(_v) = _t->can_bitrate(); break;
        case 3: *reinterpret_cast<QString*>(_v) = _t->clock_time(); break;
        case 4: *reinterpret_cast<QObject**>(_v) = _t->ribbon_view_model_qml(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->set_window_title(*reinterpret_cast<QString*>(_v)); break;
        case 1: _t->set_status_text(*reinterpret_cast<QString*>(_v)); break;
        case 2: _t->set_can_bitrate(*reinterpret_cast<QString*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *CANoeLite::ViewModels::MainWindowViewModel::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *CANoeLite::ViewModels::MainWindowViewModel::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN9CANoeLite10ViewModels19MainWindowViewModelE_t>.strings))
        return static_cast<void*>(this);
    return Core::IViewModel::qt_metacast(_clname);
}

int CANoeLite::ViewModels::MainWindowViewModel::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = Core::IViewModel::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 13)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 13;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 13)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 13;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 5;
    }
    return _id;
}

// SIGNAL 0
void CANoeLite::ViewModels::MainWindowViewModel::window_title_changed(const QString & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 0, nullptr, _t1);
}

// SIGNAL 1
void CANoeLite::ViewModels::MainWindowViewModel::status_text_changed(const QString & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 1, nullptr, _t1);
}

// SIGNAL 2
void CANoeLite::ViewModels::MainWindowViewModel::can_bitrate_changed(const QString & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 2, nullptr, _t1);
}

// SIGNAL 3
void CANoeLite::ViewModels::MainWindowViewModel::clock_time_changed(const QString & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 3, nullptr, _t1);
}

// SIGNAL 4
void CANoeLite::ViewModels::MainWindowViewModel::showMessage(const QString & _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 4, nullptr, _t1, _t2);
}

// SIGNAL 5
void CANoeLite::ViewModels::MainWindowViewModel::windowTitleChanged(const QString & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 5, nullptr, _t1);
}
QT_WARNING_POP
