#pragma once

#include <QObject>
#include <QVariantMap>
#include <memory>

#include "core/interfaces.h"

namespace CANoeLite::Services {
class RibbonConfigService;
}

namespace CANoeLite::ViewModels {

/**
 * @brief ViewModel for the ribbon interface
 */
class RibbonViewModel : public Core::IViewModel {
    Q_OBJECT

public:
    explicit RibbonViewModel(std::shared_ptr<Services::RibbonConfigService> ribbon_config_service,
                             QObject* parent = nullptr);
    ~RibbonViewModel() override = default;

    /**
     * @brief Get the ribbon configuration data
     */
    QVariantMap ribbon_data() const;

    /**
     * @brief Get command for a specific action
     */
    std::shared_ptr<Core::ICommand> get_command(const QString& action) const;

    /**
     * @brief Register a command for an action
     */
    void register_command(const QString& action, std::shared_ptr<Core::ICommand> command);

    /**
     * @brief Properties
     */
    bool quick_access_visible() const { return quick_access_visible_; }
    QString current_tab() const { return current_tab_; }

public slots:
    void set_quick_access_visible(bool visible);
    void set_current_tab(const QString& tab);
    void execute_action(const QString& action);

signals:
    void quick_access_visibility_changed(bool visible);
    void current_tab_changed(const QString& tab);
    void action_executed(const QString& action);

private:
    std::shared_ptr<Services::RibbonConfigService> ribbon_config_service_;
    QHash<QString, std::shared_ptr<Core::ICommand>> commands_;

    bool quick_access_visible_;
    QString current_tab_;
};

}  // namespace CANoeLite::ViewModels
