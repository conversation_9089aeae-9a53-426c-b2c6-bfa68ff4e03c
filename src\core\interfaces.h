#pragma once

#include <QObject>
#include <QString>
#include <QVariant>
#include <functional>
#include <memory>

/**
 * @brief Core interfaces for the CANoeLite application following MVVM pattern
 */

namespace CANoeLite::Core {

/**
 * @brief Base interface for all commands in the application
 */
class ICommand {
public:
    virtual ~ICommand() = default;
    virtual void execute() = 0;
    virtual bool can_execute() const = 0;
};

/**
 * @brief Template for parameterized commands
 */
template <typename T>
class IParameterizedCommand {
public:
    virtual ~IParameterizedCommand() = default;
    virtual void execute(const T& parameter) = 0;
    virtual bool can_execute(const T& parameter) const = 0;
};

/**
 * @brief Observer interface for notifications
 */
template <typename T>
class IObserver {
public:
    virtual ~IObserver() = default;
    virtual void on_notify(const T& data) = 0;
};

/**
 * @brief Subject interface for observable objects
 */
template <typename T>
class ISubject {
public:
    virtual ~ISubject() = default;
    virtual void subscribe(std::shared_ptr<IObserver<T>> observer) = 0;
    virtual void unsubscribe(std::shared_ptr<IObserver<T>> observer) = 0;
    virtual void notify(const T& data) = 0;
};

/**
 * @brief Base interface for all view models
 */
class IViewModel : public QObject {
    Q_OBJECT

public:
    explicit IViewModel(QObject* parent = nullptr) : QObject(parent) {}
    virtual ~IViewModel() = default;

signals:
    void property_changed(const QString& property_name, const QVariant& new_value);
};

/**
 * @brief Interface for dock widget management
 */
class IDockManager {
public:
    virtual ~IDockManager() = default;
    virtual void register_dock_widget(const QString& id, QWidget* widget) = 0;
    virtual void show_dock_widget(const QString& id) = 0;
    virtual void hide_dock_widget(const QString& id) = 0;
    virtual void close_dock_widget(const QString& id) = 0;
    virtual void save_layout() = 0;
    virtual void restore_layout() = 0;
};

/**
 * @brief Interface for ribbon configuration service
 */
class IRibbonConfigService {
public:
    virtual ~IRibbonConfigService() = default;
    virtual void load_configuration() = 0;
    virtual void save_configuration() = 0;
    virtual QVariantMap get_ribbon_data() const = 0;
    virtual void set_ribbon_data(const QVariantMap& data) = 0;
};

/**
 * @brief Interface for settings persistence
 */
class ISettingsService {
public:
    virtual ~ISettingsService() = default;
    virtual QVariant get_value(const QString& key, const QVariant& default_value = QVariant()) const = 0;
    virtual void set_value(const QString& key, const QVariant& value) = 0;
    virtual void sync() = 0;
};

/**
 * @brief Simple command implementation using std::function
 */
class RelayCommand : public ICommand {
private:
    std::function<void()> execute_func_;
    std::function<bool()> can_execute_func_;

public:
    RelayCommand(std::function<void()> execute_func, std::function<bool()> can_execute_func = []() { return true; })
        : execute_func_(std::move(execute_func)), can_execute_func_(std::move(can_execute_func)) {}

    void execute() override { execute_func_(); }
    bool can_execute() const override { return can_execute_func_(); }
};

}  // namespace CANoeLite::Core
