/****************************************************************************
** Resource object code
**
** Created by: The Resource Compiler for Qt version 6.9.1
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#ifdef _MSC_VER
// disable informational message "function ... selected for automatic inline expansion"
#pragma warning (disable: 4711)
#endif

static const unsigned char qt_resource_data[] = {
  // ribbon.json
  0x0,0x0,0x1,0x4d,
  0x28,
  0xb5,0x2f,0xfd,0x60,0xa6,0x9,0x1d,0xa,0x0,0x92,0xa,0x20,0x18,0x80,0xcf,0x1,
  0xa4,0x60,0xc4,0xca,0x26,0xf9,0x9d,0xd4,0xa8,0x66,0x5,0x6,0x26,0x50,0xcd,0x40,
  0xd9,0x27,0x90,0xd4,0x9,0x40,0x6,0xa8,0x53,0x99,0x38,0x57,0xbe,0x8,0x57,0x69,
  0xaf,0xa7,0x67,0xc5,0xfd,0x1d,0xda,0xf7,0xfd,0xb4,0x74,0x2b,0xc7,0xb1,0x5a,0x7a,
  0x28,0xa7,0xe5,0x75,0x2f,0xcd,0xd7,0xaf,0x97,0x3c,0xc4,0x8e,0xfd,0x4e,0x3e,0xa,
  0xae,0xff,0x94,0xaf,0x70,0x43,0xd8,0x4,0x6c,0xc3,0x1,0x8d,0x6b,0x95,0x4a,0xb3,
  0x46,0x24,0xc8,0x1c,0x7d,0x1b,0xed,0x43,0xc9,0xdc,0xd0,0x6,0xce,0xd2,0x83,0xfb,
  0x9f,0x7a,0x2,0xe7,0xdc,0xfb,0x39,0x69,0x44,0x1a,0xf3,0x94,0x33,0x6,0x30,0x59,
  0xdf,0x16,0xae,0x9,0xce,0x9f,0x7c,0xd7,0x5a,0x6b,0x0,0x16,0x52,0xa0,0xe0,0x6a,
  0xa4,0xaa,0xb0,0x71,0x60,0x24,0x90,0x49,0x44,0xf3,0xd1,0xc0,0x4,0x81,0xe2,0x22,
  0x1b,0x63,0x39,0x80,0xb2,0x4e,0x49,0x68,0x1a,0xa2,0xf9,0x20,0xb2,0x44,0xb7,0x48,
  0x6e,0x98,0xaa,0x1b,0xa5,0x8a,0x3f,0x92,0x9e,0xfd,0x88,0x1e,0xc4,0xe,0x75,0x41,
  0x1f,0x45,0xa3,0xd0,0xa7,0xa4,0xd7,0x84,0x27,0xa3,0xc3,0x52,0xcc,0xad,0x9f,0xe7,
  0xb7,0x5b,0x73,0xf3,0x2,0xe1,0x16,0xf,0x4,0x5a,0xc0,0x4c,0x52,0xd5,0xca,0xdc,
  0x59,0x56,0x5a,0x94,0x39,0x5e,0x76,0xbc,0x69,0x6a,0xd2,0xfb,0xcb,0xed,0x18,0x2a,
  0xd3,0x30,0xd1,0x6d,0x44,0x44,0xe0,0x28,0x0,0xae,0x5,0xa2,0x80,0x3e,0xe0,0x48,
  0xea,0x40,0xa1,0x2b,0x4a,0x5a,0xa8,0x40,0x7e,0x60,0xd9,0x82,0xf7,0xfb,0x96,0xc0,
  0xd6,0x2d,0x7e,0x2e,0xca,0xe5,0x14,0x3e,0xac,0x2c,0x82,0x2c,0x78,0x5f,0x14,0xa8,
  0xe5,0xf4,0x37,0xa4,0xdf,0x57,0x42,0x21,0x6,0xe6,0x23,0xa0,0x6b,0x71,0x7d,0xef,
  0xb6,0x6e,0x50,0xae,0x10,0x69,0x45,0x54,0xa5,0x64,0x3a,0x5b,0x97,0xb8,0xd5,0x47,
  0xe,0x41,0xea,0xb6,0x8c,0x82,0xaa,0xb8,0x99,0x11,0xe0,0x14,
  
};

static const unsigned char qt_resource_name[] = {
  // ribbon.json
  0x0,0xb,
  0x6,0xe4,0xeb,0x7e,
  0x0,0x72,
  0x0,0x69,0x0,0x62,0x0,0x62,0x0,0x6f,0x0,0x6e,0x0,0x2e,0x0,0x6a,0x0,0x73,0x0,0x6f,0x0,0x6e,
  
};

static const unsigned char qt_resource_struct[] = {
  // :
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x1,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/ribbon.json
  0x0,0x0,0x0,0x0,0x0,0x4,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x0,
0x0,0x0,0x1,0x97,0xde,0x53,0xf4,0x50,

};

#ifdef QT_NAMESPACE
#  define QT_RCC_PREPEND_NAMESPACE(name) ::QT_NAMESPACE::name
#  define QT_RCC_MANGLE_NAMESPACE0(x) x
#  define QT_RCC_MANGLE_NAMESPACE1(a, b) a##_##b
#  define QT_RCC_MANGLE_NAMESPACE2(a, b) QT_RCC_MANGLE_NAMESPACE1(a,b)
#  define QT_RCC_MANGLE_NAMESPACE(name) QT_RCC_MANGLE_NAMESPACE2( \
        QT_RCC_MANGLE_NAMESPACE0(name), QT_RCC_MANGLE_NAMESPACE0(QT_NAMESPACE))
#else
#   define QT_RCC_PREPEND_NAMESPACE(name) name
#   define QT_RCC_MANGLE_NAMESPACE(name) name
#endif

#if defined(QT_INLINE_NAMESPACE)
inline namespace QT_NAMESPACE {
#elif defined(QT_NAMESPACE)
namespace QT_NAMESPACE {
#endif

bool qRegisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
bool qUnregisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);

#if defined(__ELF__) || defined(__APPLE__)
static inline unsigned char qResourceFeatureZstd()
{
    extern const unsigned char qt_resourceFeatureZstd;
    return qt_resourceFeatureZstd;
}
#else
unsigned char qResourceFeatureZstd();
#endif

#ifdef QT_NAMESPACE
}
#endif

int QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)();
int QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qRegisterResourceData)
        (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)();
int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)()
{
    int version = 3;
    version += QT_RCC_PREPEND_NAMESPACE(qResourceFeatureZstd());
    QT_RCC_PREPEND_NAMESPACE(qUnregisterResourceData)
       (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

#ifdef __clang__
#   pragma clang diagnostic push
#   pragma clang diagnostic ignored "-Wexit-time-destructors"
#endif

namespace {
   struct initializer {
       initializer() { QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)(); }
       ~initializer() { QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)(); }
   } dummy;
}

#ifdef __clang__
#   pragma clang diagnostic pop
#endif
