import QtQuick 2.15
import QtQuick.Controls 2.15
import "../theme"
import "../components"

Rectangle {
    id: root
    
    property var viewModel: null
    property var tabs: ["Desktop 1", "Desktop 2", "Desktop 3"]
    property int currentIndex: 0
    
    function initialize() {
        if (viewModel) {
            console.log("DesktopTabs initialized")
        }
    }
    
    color: Colors.background
    border.color: Colors.border
    border.width: AppTheme.border.widthThin
    
    TabBar {
        id: tabBar
        anchors.fill: parent
        currentIndex: root.currentIndex
        
        background: Rectangle {
            color: Colors.background
        }
        
        Repeater {
            model: root.tabs
            
            DesktopTab {
                text: modelData
                closable: index > 0 // First tab is not closable
                
                onCloseRequested: {
                    // Remove tab
                    var newTabs = []
                    for (var i = 0; i < root.tabs.length; i++) {
                        if (i !== index) {
                            newTabs.push(root.tabs[i])
                        }
                    }
                    root.tabs = newTabs
                    
                    // Adjust current index if necessary
                    if (root.currentIndex >= index && root.currentIndex > 0) {
                        root.currentIndex--
                    }
                }
            }
        }
        
        // Add new tab button
        TabButton {
            text: "+"
            width: 30
            
            background: Rectangle {
                color: parent.hovered ? Colors.hover : "transparent"
                radius: AppTheme.radius.sm
                
                Behavior on color {
                    ColorAnimation {
                        duration: AppTheme.animations.hover.duration
                        easing.type: AppTheme.animations.hover.easing
                    }
                }
            }
            
            contentItem: Text {
                text: parent.text
                font.family: AppTheme.typography.primaryFont
                font.pixelSize: AppTheme.typography.titleMedium
                font.weight: AppTheme.typography.weightBold
                color: Colors.onBackground
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
            
            onClicked: {
                // Add new tab
                var newTabs = root.tabs.slice()
                newTabs.push("Desktop " + (newTabs.length + 1))
                root.tabs = newTabs
                root.currentIndex = newTabs.length - 1
            }
        }
        
        onCurrentIndexChanged: {
            root.currentIndex = currentIndex
        }
    }
}
