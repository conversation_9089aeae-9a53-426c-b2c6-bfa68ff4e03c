#include <QGuiApplication>
#include <QQmlApplicationEngine>
#include <QQmlContext>
#include <QtQml>

#include "core/application.h"
#include "view_models/main_window_view_model.h"
#include "view_models/ribbon_view_model.h"

/**
 * @brief Main entry point for CANoeLite QML application
 */
int main(int argc, char* argv[]) {
    QGuiApplication app(argc, argv);

    // Set application properties
    app.setApplicationName("CANoeLite");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("CANoeLite");

    // Register QML types
    qmlRegisterType<CANoeLite::ViewModels::MainWindowViewModel>("CANoeLite", 1, 0, "MainWindowViewModel");
    qmlRegisterType<CANoeLite::ViewModels::RibbonViewModel>("CANoeLite", 1, 0, "RibbonViewModel");

    // Create application core
    CANoeLite::Core::Application canoeLiteApp;
    if (!canoeLiteApp.initialize()) {
        return -1;
    }

    // Create QML engine
    QQmlApplicationEngine engine;

    // Expose application and view models to QML
    engine.rootContext()->setContextProperty("application", &canoeLiteApp);
    engine.rootContext()->setContextProperty("mainViewModel", canoeLiteApp.main_window_view_model().get());

    // Load main QML file
    const QUrl url(QStringLiteral("qrc:/qml/main.qml"));
    QObject::connect(&engine, &QQmlApplicationEngine::objectCreated,
                     &app, [url](QObject *obj, const QUrl &objUrl) {
        if (!obj && url == objUrl)
            QCoreApplication::exit(-1);
    }, Qt::QueuedConnection);

    engine.load(url);

    return app.exec();
}
